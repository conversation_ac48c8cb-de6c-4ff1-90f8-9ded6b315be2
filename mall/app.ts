App({
  globalData: {
    userInfo: null,
    userId: null,
    openId: null,
    sessionKey: null,
    isLoggedIn: false,
    cartItems: [],
    repairInfo: null,
    primaryColor: '#1e88e5'
  },
  onLaunch: function () {
    // 初始化应用时执行
    console.log('App launched');

    // 检查登录状态
    tt.checkSession({
      success: () => {
        // 登录态有效
        console.log('Session valid');
        // 从本地存储中获取用户信息
        this.getUserInfo();
      },
      fail: () => {
        // 登录态过期，重新登录
        this.login();
      }
    });
  },

  login: function() {
    const api = require('./utils/api');


    tt.login({
      success: (res) => {
        if (res.code) {
          console.log('Login successful, code:', res.code);

          // 获取用户信息
          tt.getUserProfile({
            desc: '用于完善会员资料', // 抖音小程序特有参数，用于告知用户获取信息的用途
            success: (userRes) => {
              // 发送 code 和用户信息到后端进行抖音小程序登录
              api.login(res.code, userRes.userInfo).then(loginRes => {
                if (loginRes.success) {
                  // 登录成功，保存用户信息
                  this.globalData.userInfo = loginRes.userInfo;
                  this.globalData.userId = loginRes.userId;
                  this.globalData.openId = loginRes.openId;
                  this.globalData.sessionKey = loginRes.sessionKey;
                  this.globalData.isLoggedIn = true;

                  console.log('登录成功，用户ID:', loginRes.userId);

                  // 存储用户信息到本地
                  tt.setStorage({
                    key: 'userInfo',
                    data: {
                      userInfo: loginRes.userInfo,
                      userId: loginRes.userId,
                      openId: loginRes.openId,
                      sessionKey: loginRes.sessionKey
                    }
                  });
                } else {
                  console.error('登录失败:', loginRes.message);
                  tt.showToast({
                    title: '登录失败',
                    icon: 'none'
                  });
                }
              }).catch(err => {
                console.error('登录请求失败:', err);
                tt.showToast({
                  title: '登录失败，请重试',
                  icon: 'none'
                });
              });
            },
            fail: (err) => {
              console.error('Get user info failed:', err);
              // 用户拒绝授权，弹出提示
              tt.showModal({
                title: '提示',
                content: '需要您的授权才能正常使用小程序功能',
                showCancel: false
              });
            }
          });
        } else {
          console.log('Login failed:', res.errMsg);
          tt.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('Login error:', err);
        tt.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 获取用户信息
  getUserInfo: function(cb) {
    // 先从全局变量中获取
    if (this.globalData.userInfo) {
      typeof cb === 'function' && cb(this.globalData.userInfo);
      return;
    }

    // 尝试从本地存储中获取
    tt.getStorage({
      key: 'userInfo',
      success: (res) => {
        if (res.data && res.data.userInfo) {
          this.globalData.userInfo = res.data.userInfo;
          this.globalData.userId = res.data.userId;
          this.globalData.openId = res.data.openId;
          this.globalData.sessionKey = res.data.sessionKey;
          this.globalData.isLoggedIn = true;

          typeof cb === 'function' && cb(this.globalData.userInfo);
        } else {
          // 本地没有用户信息，重新登录
          this.login();
        }
      },
      fail: () => {
        // 获取本地存储失败，重新登录
        this.login();
      }
    });
  },

  // 添加商品到购物车
  addToCart: function(product, quantity = 1) {
    let cartItems = this.globalData.cartItems;
    let found = false;

    // 检查商品是否已在购物车中
    for (let i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === product.id) {
        cartItems[i].quantity += quantity;
        found = true;
        break;
      }
    }

    // 如果是新商品，添加到购物车
    if (!found) {
      product.quantity = quantity;
      cartItems.push(product);
    }

    // 更新全局购物车数据
    this.globalData.cartItems = cartItems;
    return cartItems;
  },

  // 从购物车移除商品
  removeFromCart: function(productId) {
    let cartItems = this.globalData.cartItems;
    this.globalData.cartItems = cartItems.filter(item => item.id !== productId);
    return this.globalData.cartItems;
  },

  // 更新购物车商品数量
  updateCartItemQuantity: function(productId, quantity) {
    let cartItems = this.globalData.cartItems;
    for (let i = 0; i < cartItems.length; i++) {
      if (cartItems[i].id === productId) {
        cartItems[i].quantity = quantity;
        break;
      }
    }
    this.globalData.cartItems = cartItems;
    return cartItems;
  },

  // 保存维修信息
  saveRepairInfo: function(repairInfo) {
    this.globalData.repairInfo = repairInfo;
  }
})
