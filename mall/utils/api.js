// API基础URL
// 注意：在真实环境中，这里应该是你的服务器地址
// 抖音小程序开发者工具中可以使用localhost
// 真机调试时需要使用内网IP或外网域名
const BASE_URL = 'http://localhost:8080/api';

// 请求方法
const request = (url, method, data) => {
  return new Promise((resolve, reject) => {
    tt.request({
      url: BASE_URL + url,
      method: method,
      data: data,
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res.data || { message: '请求失败' });
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 用户登录
const login = (code, userInfo) => {
  return request('/user/login', 'POST', { code, userInfo });
};

// 简化登录，只需要code就可以获取openId
const code2Session = (code) => {
  return request('/user/code2Session', 'POST', { code });
};

// 获取用户信息
const getUserInfo = (userId) => {
  return request(`/user/${userId}`, 'GET');
};

// 更新用户手机号
const updateUserPhone = (userId, phone) => {
  return request('/user/updatePhone', 'POST', { userId, phone });
};

// 获取用户地址列表
const getAddressList = (openId) => {
  return request(`/address/list?openId=${openId}`, 'GET');
};

// 获取用户默认地址
const getDefaultAddress = (openId) => {
  return request(`/address/default?openId=${openId}`, 'GET');
};

// 保存地址（新增或修改）
const saveAddress = (addressData) => {
  return request('/address/save', 'POST', addressData);
};

// 删除地址
const deleteAddress = (id, openId) => {
  return request(`/address/${id}?openId=${openId}`, 'DELETE');
};

// 设置默认地址
const setDefaultAddress = (id, openId) => {
  return request(`/address/${id}/default?openId=${openId}`, 'POST');
};

// 创建维修订单
const createRepairOrder = (repairOrderData) => {
  return request('/repair/create', 'POST', repairOrderData);
};

// 获取用户维修订单列表
const getRepairOrderList = (openId, status) => {
  let url = `/repair/list?openId=${openId}`;
  if (status && status !== 'all') {
    url += `&status=${status}`;
  }
  return request(url, 'GET');
};

// 获取维修订单详情
const getRepairOrderDetail = (orderNo, openId) => {
  return request(`/repair/detail?orderNo=${orderNo}&openId=${openId}`, 'GET');
};

// 取消维修订单
const cancelRepairOrder = (orderNo, openId) => {
  return request(`/repair/cancel?orderNo=${orderNo}&openId=${openId}`, 'POST');
};

// 获取用户维修订单统计
const getRepairOrderStats = (openId) => {
  return request(`/repair/stats?openId=${openId}`, 'GET');
};

// ========== 商品相关API ==========

// 获取商品分类列表
const getProductCategories = () => {
  return request('/products/categories', 'GET');
};

// 获取商品列表
const getProductList = (categoryId = 0, sortType = 'default', priceOrder = 'desc') => {
  return request(`/products/list?categoryId=${categoryId}&sortType=${sortType}&priceOrder=${priceOrder}`, 'GET');
};

// 获取商品详情
const getProductDetail = (id) => {
  return request(`/products/detail/${id}`, 'GET');
};

// 获取商品评价列表
const getProductReviews = (id) => {
  return request(`/products/${id}/reviews`, 'GET');
};

// ========== 工程师相关API ==========

// 获取所有已审核通过的工程师列表
const getApprovedEngineers = () => {
  return request('/engineers/approved', 'GET');
};

// 获取在线且可接单的工程师列表
const getAvailableEngineers = () => {
  return request('/engineers/available', 'GET');
};

// 根据专业领域查询工程师
const getEngineersBySpecialty = (specialty) => {
  return request(`/engineers/specialty/${specialty}`, 'GET');
};

// 获取工程师统计信息
const getEngineerStats = () => {
  return request('/engineers/stats', 'GET');
};

// 获取工程师详情
const getEngineerDetail = (id) => {
  return request(`/engineers/detail/${id}`, 'GET');
};

// ========== 服务网点相关API ==========

// 获取所有已审核通过的服务网点列表
const getApprovedServiceCenters = () => {
  return request('/service-centers/approved', 'GET');
};

// 获取推荐的服务网点列表
const getFeaturedServiceCenters = () => {
  return request('/service-centers/featured', 'GET');
};

// 根据城市查询服务网点
const getServiceCentersByCity = (city) => {
  return request(`/service-centers/city/${city}`, 'GET');
};

// 根据服务类型查询服务网点
const getServiceCentersByServiceType = (serviceType) => {
  return request(`/service-centers/service-type/${serviceType}`, 'GET');
};

// 搜索服务网点
const searchServiceCenters = (keyword) => {
  return request(`/service-centers/search?keyword=${keyword}`, 'GET');
};

// 根据位置范围查询服务网点
const getServiceCentersByLocation = (minLat, maxLat, minLng, maxLng) => {
  return request(`/service-centers/location?minLat=${minLat}&maxLat=${maxLat}&minLng=${minLng}&maxLng=${maxLng}`, 'GET');
};

// 获取服务网点统计信息
const getServiceCenterStats = () => {
  return request('/service-centers/stats', 'GET');
};

// 获取服务网点详情
const getServiceCenterDetail = (id) => {
  return request(`/service-centers/detail/${id}`, 'GET');
};

// ========== 文件上传相关API ==========

// 上传单个图片
const uploadImage = (filePath) => {
  return new Promise((resolve, reject) => {
    tt.uploadFile({
      url: `${BASE_URL}/upload/image`,
      filePath: filePath,
      name: 'file',
      header: {
        'Content-Type': 'multipart/form-data'
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          resolve(data);
        } catch (e) {
          reject(new Error('响应数据解析失败'));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 上传多个图片
const uploadImages = (filePaths) => {
  return Promise.all(filePaths.map(filePath => uploadImage(filePath)));
};

// ========== 申请提交相关API ==========

// 提交充电站入驻申请
const submitStationApplication = (applicationData) => {
  return request('/applications/station', 'POST', applicationData);
};

// 提交工程师入驻申请
const submitEngineerApplication = (applicationData) => {
  return request('/applications/engineer', 'POST', applicationData);
};

// 导出API方法
module.exports = {
  login,
  code2Session,
  getUserInfo,
  updateUserPhone,
  getAddressList,
  getDefaultAddress,
  saveAddress,
  deleteAddress,
  setDefaultAddress,
  createRepairOrder,
  getRepairOrderList,
  getRepairOrderDetail,
  cancelRepairOrder,
  getRepairOrderStats,
  // 商品相关API
  getProductCategories,
  getProductList,
  getProductDetail,
  getProductReviews,
  // 工程师相关API
  getApprovedEngineers,
  getAvailableEngineers,
  getEngineersBySpecialty,
  getEngineerStats,
  getEngineerDetail,
  // 服务网点相关API
  getApprovedServiceCenters,
  getFeaturedServiceCenters,
  getServiceCentersByCity,
  getServiceCentersByServiceType,
  searchServiceCenters,
  getServiceCentersByLocation,
  getServiceCenterStats,
  getServiceCenterDetail,
  // 文件上传相关API
  uploadImage,
  uploadImages,
  // 申请提交相关API
  submitStationApplication,
  submitEngineerApplication
};
