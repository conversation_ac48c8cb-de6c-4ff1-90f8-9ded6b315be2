page {
  background-color: #f5f5f5;
}

.container {
  padding-bottom: 20rpx;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  gap: 20rpx;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  height: 70rpx;
  padding: 0 80rpx 0 20rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  font-size: 28rpx;
}

.search-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 32rpx;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 15rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
}

/* 专业领域标签 */
.specialty-tabs {
  background-color: #fff;
  white-space: nowrap;
  border-bottom: 1rpx solid #f0f0f0;
}

.specialty-tab-wrapper {
  display: inline-flex;
  padding: 20rpx;
  gap: 20rpx;
}

.specialty-tab {
  padding: 12rpx 24rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
}

.specialty-tab.active {
  background-color: #1890ff;
  color: #fff;
}

/* 排序栏 */
.sort-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.sort-options {
  display: flex;
  gap: 30rpx;
}

.sort-option {
  font-size: 26rpx;
  color: #666;
  padding: 8rpx 0;
}

.sort-option.active {
  color: #1890ff;
  font-weight: 600;
}

.result-count {
  font-size: 24rpx;
  color: #999;
}

/* 筛选面板 */
.filter-panel {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.filter-panel.show {
  max-height: 500rpx;
}

.filter-section {
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 15rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.filter-option {
  padding: 8rpx 16rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
}

.filter-option.active {
  background-color: #1890ff;
  color: #fff;
}

.filter-actions {
  display: flex;
  padding: 20rpx;
  gap: 20rpx;
}

.filter-reset,
.filter-confirm {
  flex: 1;
  text-align: center;
  padding: 15rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.filter-reset {
  background-color: #f5f5f5;
  color: #666;
}

.filter-confirm {
  background-color: #1890ff;
  color: #fff;
}

/* 工程师列表 */
.engineer-list {
  padding: 0 20rpx;
}

.loading,
.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.engineer-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.engineer-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.engineer-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.engineer-basic {
  flex: 1;
}

.engineer-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.engineer-experience {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.engineer-rating {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.stars {
  display: flex;
}

.stars .icon-star {
  color: #ffd700;
  font-size: 24rpx;
  margin-right: 2rpx;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
}

.engineer-actions {
  flex-shrink: 0;
}

.contact-btn {
  padding: 12rpx 24rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
}

.engineer-bio {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.engineer-specialties {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.specialty-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.engineer-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 15rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.stat-value {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}
