<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <input 
        class="search-input" 
        placeholder="搜索技师姓名或专业领域" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
      />
      <text class="iconfont icon-search search-icon"></text>
    </view>
    <view class="filter-btn" bindtap="toggleFilter">
      <text class="iconfont icon-filter"></text>
      <text>筛选</text>
    </view>
  </view>

  <!-- 专业领域标签 -->
  <scroll-view class="specialty-tabs" scroll-x="true">
    <view class="specialty-tab-wrapper">
      <view 
        class="specialty-tab {{currentSpecialty === item.value ? 'active' : ''}}" 
        tt:for="{{specialties}}" 
        tt:key="value"
        bindtap="onSpecialtyChange"
        data-specialty="{{item.value}}"
      >
        {{item.label}}
      </view>
    </view>
  </scroll-view>

  <!-- 排序选项 -->
  <view class="sort-bar">
    <view class="sort-options">
      <view 
        class="sort-option {{sortType === 'rating' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="rating"
      >
        评分最高
      </view>
      <view 
        class="sort-option {{sortType === 'experience' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="experience"
      >
        经验最多
      </view>
      <view 
        class="sort-option {{sortType === 'orders' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="orders"
      >
        服务最多
      </view>
    </view>
    <view class="result-count">共{{filteredEngineers.length}}名技师</view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel {{filterOptions.showFilter ? 'show' : ''}}" tt:if="{{filterOptions.showFilter}}">
    <view class="filter-section">
      <view class="filter-title">工作经验</view>
      <view class="filter-options">
        <view 
          class="filter-option {{filterOptions.selectedExperience === item.value ? 'active' : ''}}"
          tt:for="{{filterOptions.experienceRanges}}"
          tt:key="value"
          bindtap="onExperienceChange"
          data-experience="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>
    
    <view class="filter-section">
      <view class="filter-title">评分</view>
      <view class="filter-options">
        <view 
          class="filter-option {{filterOptions.selectedRating === item.value ? 'active' : ''}}"
          tt:for="{{filterOptions.ratingRanges}}"
          tt:key="value"
          bindtap="onRatingChange"
          data-rating="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>
    
    <view class="filter-actions">
      <view class="filter-reset" bindtap="resetFilter">重置</view>
      <view class="filter-confirm" bindtap="toggleFilter">确定</view>
    </view>
  </view>

  <!-- 工程师列表 -->
  <view class="engineer-list">
    <view class="loading" tt:if="{{loading}}">
      <text>加载中...</text>
    </view>
    
    <view class="empty" tt:if="{{!loading && filteredEngineers.length === 0}}">
      <text>暂无符合条件的技师</text>
    </view>
    
    <view 
      class="engineer-card" 
      tt:for="{{filteredEngineers}}" 
      tt:key="id"
      bindtap="viewEngineerDetail"
      data-id="{{item.id}}"
    >
      <view class="engineer-header">
        <image src="{{item.avatar}}" class="engineer-avatar" mode="aspectFill" />
        <view class="engineer-basic">
          <view class="engineer-name">{{item.name}}</view>
          <view class="engineer-experience">{{item.experienceYears}}年经验 | {{item.education}}</view>
          <view class="engineer-rating">
            <view class="stars">
              <text class="iconfont icon-star" tt:for="{{item.ratingStars}}" tt:key="*this"></text>
            </view>
            <text class="rating-text">{{item.rating}} ({{item.totalOrders}}次服务)</text>
          </view>
        </view>
        <view class="engineer-actions">
          <view 
            class="contact-btn" 
            bindtap="contactEngineer"
            data-phone="{{item.phone}}"
            data-name="{{item.name}}"
            catchtap="true"
          >
            联系
          </view>
        </view>
      </view>
      
      <view class="engineer-bio" tt:if="{{item.bio}}">{{item.bio}}</view>
      <view class="engineer-specialties" tt:if="{{item.specialties.length > 0}}">
        <text class="specialty-tag" tt:for="{{item.specialties}}" tt:key="*this" tt:if="{{index < 4}}">
          {{item}}
        </text>
      </view>
      
      <view class="engineer-stats">
        <view class="stat-item">
          <text class="stat-label">成功率</text>
          <text class="stat-value">{{item.successRate}}%</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">服务费</text>
          <text class="stat-value">¥{{item.serviceFee}}</text>
        </view>
        <view class="stat-item" tt:if="{{item.hourlyRate > 0}}">
          <text class="stat-label">时薪</text>
          <text class="stat-value">¥{{item.hourlyRate}}/时</text>
        </view>
      </view>
    </view>
  </view>
</view>
