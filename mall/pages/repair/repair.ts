const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    banners: [
      {
        id: 1,
        image: '/images/轮播图/轮播图6.png'
      },
      {
        id: 2,
        image: '/images/轮播图/轮播图5.png'
      }
    ],
    faultTypes: [
      {
        id: 1,
        name: '无法充电',
        icon: 'icon-bolt',
        image: '/images/icons/无法充电.png',
        type: 'no_charging'
      },
      {
        id: 2,
        name: '充电慢',
        icon: 'icon-tachometer',
        image: '/images/icons/充电慢.png',
        type: 'slow_charging'
      },
      {
        id: 3,
        name: '报错代码',
        icon: 'icon-exclamation-triangle',
        image: '/images/icons/错误代码.png',
        type: 'error_code'
      },
      {
        id: 4,
        name: '接口损坏',
        icon: 'icon-plug',
        image: '/images/icons/接口损坏.png',
        type: 'port_damage'
      },
      {
        id: 5,
        name: '无法启动',
        icon: 'icon-power-off',
        image: '/images/icons/无法启动.png',
        type: 'not_starting'
      },
      {
        id: 6,
        name: '过热',
        icon: 'icon-thermometer',
        image: '/images/icons/过热.png',
        type: 'overheating'
      },
      {
        id: 7,
        name: '显示故障',
        icon: 'icon-mobile',
        image: '/images/icons/显示故障.png',
        type: 'display_issue'
      },
      {
        id: 8,
        name: '其他故障',
        icon: 'icon-question-circle',
        image: '/images/icons/其他故障.png',
        type: 'other'
      }
    ],
    priceList: [
      {
        name: '充电枪更换',
        price: '200-500'
      },
      {
        name: '控制模块维修',
        price: '300-800'
      },
      {
        name: '线缆更换',
        price: '150-400'
      },
      {
        name: '显示屏维修',
        price: '200-600'
      },
      {
        name: '软件故障排查',
        price: '100-300'
      }
    ],
    engineers: [],
    engineerStats: {
      total: 0,
      approved: 0,
      online: 0
    }
  },

  onLoad: function () {
    console.log('Repair page loaded');
    // 加载工程师数据
    this.loadEngineers();
    this.loadEngineerStats();
  },

  onShow: function () {
    // 更新自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      });
      console.log('维修页更新tabBar成功');
    } else {
      console.log('维修页获取tabBar失败');
    }
  },

  // 跳转到维修表单页面
  goToRepairForm: function(e) {
    const type = e.currentTarget.dataset.type;
    tt.navigateTo({
      url: `/pages/repair_form/repair_form?type=${type}`
    });
  },

  // 跳转到服务网点页面
  goToServiceCenters: function() {
    tt.navigateTo({
      url: '/pages/service_centers/service_centers'
    });
  },

  // 跳转到在线咨询页面
  goToOnlineConsult: function() {
    tt.showToast({
      title: '在线咨询功能开发中',
      icon: 'none'
    });
  },

  // 加载工程师数据
  loadEngineers: function() {
    api.getApprovedEngineers().then(res => {
      if (res.success) {
        // 处理工程师数据，转换为页面需要的格式
        const engineers = res.data.map(engineer => {
          // 解析JSON字段
          let specialties = [];
          let skills = [];
          let workAreas = [];

          try {
            specialties = JSON.parse(engineer.specialties || '[]');
            skills = JSON.parse(engineer.skills || '[]');
            workAreas = JSON.parse(engineer.workAreas || '[]');
          } catch (e) {
            console.error('解析工程师JSON字段失败:', e);
          }

          return {
            id: engineer.id,
            name: engineer.name,
            avatar: engineer.avatar,
            rating: Math.floor(engineer.rating || 5), // 用于显示星星数量
            ratingValue: (engineer.rating || 5.0).toFixed(1),
            reviewCount: engineer.totalOrders || 0,
            experience: `${engineer.experienceYears || 0}年维修经验`,
            title: specialties.length > 0 ? specialties[0] : '专业技师',
            bio: engineer.bio || '',
            isOnline: engineer.isOnline,
            isAvailable: engineer.isAvailable,
            workTime: engineer.workTime,
            hourlyRate: engineer.hourlyRate,
            serviceFee: engineer.serviceFee,
            specialties: specialties,
            skills: skills,
            workAreas: workAreas
          };
        });

        this.setData({
          engineers: engineers
        });
      } else {
        console.error('获取工程师列表失败:', res.message);
      }
    }).catch(err => {
      console.error('获取工程师列表失败:', err);
    });
  },

  // 加载工程师统计信息
  loadEngineerStats: function() {
    api.getEngineerStats().then(res => {
      if (res.success) {
        this.setData({
          engineerStats: res.data
        });
      } else {
        console.error('获取工程师统计失败:', res.message);
      }
    }).catch(err => {
      console.error('获取工程师统计失败:', err);
    });
  },

  // 查看全部技师
  viewAllEngineers: function() {
    tt.navigateTo({
      url: '/pages/engineer_list/engineer_list'
    });
  }
})
