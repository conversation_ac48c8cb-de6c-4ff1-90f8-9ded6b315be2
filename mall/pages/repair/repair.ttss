.container {
  padding: 30rpx;
}

.repair-option {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.repair-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 32rpx;
  color: #1e88e5;
  font-size: 48rpx;
}

.repair-info {
  flex: 1;
}

.repair-title {
  font-weight: 600;
  margin-bottom: 8rpx;
  font-size: 32rpx;
}

.repair-desc {
  font-size: 28rpx;
  color: #757575;
}

.fault-grid {
  display: flex;
  flex-wrap: wrap;
}

.fault-type {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
}

.fault-icon {
  width: 100rpx;
  height: 100rpx;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  color: #1e88e5;
  font-size: 40rpx;
}

.fault-name {
  font-size: 24rpx;
  text-align: center;
}

.price-table {
  border: 1px solid #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
}

.price-row {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.price-row:last-child {
  border-bottom: none;
}

.price-name {
  flex: 1;
  padding: 16rpx;
  font-size: 28rpx;
}

.price-value {
  width: 200rpx;
  padding: 16rpx;
  text-align: right;
  font-size: 28rpx;
  font-weight: 500;
}

.engineer {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1px solid #e0e0e0;
}

.engineer:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.engineer-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.engineer-info {
  flex: 1;
}

.engineer-name {
  font-weight: 600;
  margin-bottom: 8rpx;
}

.ml-1 {
  margin-left: 8rpx;
}

/* 工程师状态相关样式 */
.engineer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.engineer-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-dot.online {
  background-color: #52c41a;
}

.status-dot.offline {
  background-color: #d9d9d9;
}

.engineer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-top: 10rpx;
}

.tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.text-success {
  color: #52c41a;
}

.text-primary {
  color: #1890ff;
}
