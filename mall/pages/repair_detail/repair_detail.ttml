<view class="container">
  <view class="status-bar status-{{order.status}}">
    <view class="status-icon">
      <view class="icon-wrapper">
        <image class="icon" src="/images/icons/订单.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="status-text">{{statusText[order.status]}}</view>
  </view>

  <view class="card order-info">
    <view class="card-title">订单信息</view>
    <view class="info-item">
      <text class="label">订单编号</text>
      <text class="value">{{order.orderNo}}</text>
    </view>
    <view class="info-item">
      <text class="label">创建时间</text>
      <text class="value">{{order.createdAtFormatted}}</text>
    </view>
    <view class="info-item">
      <text class="label">预约时间</text>
      <text class="value">{{order.appointmentTime}}</text>
    </view>
  </view>

  <view class="card fault-info">
    <view class="card-title">故障信息</view>
    <view class="info-item">
      <text class="label">故障类型</text>
      <text class="value">{{faultTypeMap[order.faultType] || '未知故障'}}</text>
    </view>
    <view class="info-item">
      <text class="label">充电桩型号</text>
      <text class="value">{{order.model}}</text>
    </view>
    <view class="info-item">
      <text class="label">故障描述</text>
      <text class="value">{{order.description}}</text>
    </view>
    <view class="info-item" tt:if="{{order.images && order.images.length > 0}}">
      <text class="label">故障图片</text>
      <view class="image-list">
        <image
          tt:for="{{order.images}}"
          tt:key="index"
          class="fault-image"
          src="{{item}}"
          mode="aspectFill"
          bindtap="previewImage"
          data-url="{{item}}"
        ></image>
      </view>
    </view>
  </view>

  <view class="card service-info">
    <view class="card-title">服务信息</view>
    <view class="info-item">
      <text class="label">服务方式</text>
      <text class="value">{{order.serviceType === 'home' ? '上门维修' : '远程指导'}}</text>
    </view>
    <view class="info-item">
      <text class="label">联系人</text>
      <text class="value">{{order.name}}</text>
    </view>
    <view class="info-item">
      <text class="label">联系电话</text>
      <text class="value">{{order.phone}}</text>
    </view>
    <view class="info-item" tt:if="{{order.serviceType === 'home'}}">
      <text class="label">服务地址</text>
      <text class="value">{{order.fullAddress}}</text>
    </view>
  </view>

  <view class="card engineer-info" tt:if="{{order.status !== 'pending' && order.engineerName}}">
    <view class="card-title">工程师信息</view>
    <view class="engineer">
      <image class="engineer-avatar" src="{{order.engineerAvatar || '/images/icons/engineer.png'}}" mode="aspectFill"></image>
      <view class="engineer-detail">
        <view class="engineer-name">{{order.engineerName}}</view>
        <view class="engineer-title">充电桩维修工程师</view>
      </view>
      <view class="engineer-contact" tt:if="{{order.status !== 'completed'}}">
        <button class="contact-btn" bindtap="callEngineer" data-phone="{{order.engineerPhone}}">
          <image class="contact-icon" src="/images/icons/phone.png" mode="aspectFit"></image>
        </button>
      </view>
    </view>
  </view>

  <!-- 维修进度 -->
  <view class="card progress-info" tt:if="{{order.status !== 'pending'}}">
    <view class="card-title">维修进度</view>
    <view class="progress-timeline">
      <view class="timeline-item {{order.status === 'accepted' || order.status === 'processing' || order.status === 'completed' ? 'active' : ''}}">
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="timeline-title">已接单</view>
          <view class="timeline-desc" tt:if="{{order.status === 'accepted' || order.status === 'processing' || order.status === 'completed'}}">工程师已接单，准备上门服务</view>
        </view>
      </view>
      <view class="timeline-item {{order.status === 'processing' || order.status === 'completed' ? 'active' : ''}}">
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="timeline-title">维修中</view>
          <view class="timeline-desc" tt:if="{{order.status === 'processing' || order.status === 'completed'}}">工程师正在进行维修</view>
        </view>
      </view>
      <view class="timeline-item {{order.status === 'completed' ? 'active' : ''}}">
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="timeline-title">已完成</view>
          <view class="timeline-desc" tt:if="{{order.status === 'completed'}}">维修已完成</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 维修详情（仅已完成状态显示） -->
  <view class="card repair-detail" tt:if="{{order.status === 'completed' && order.repairDescription}}">
    <view class="card-title">维修详情</view>
    <view class="info-item" tt:if="{{order.repairResult}}">
      <text class="label">维修结果</text>
      <text class="value">{{getRepairResultText(order.repairResult)}}</text>
    </view>
    <view class="info-item" tt:if="{{order.repairTime}}">
      <text class="label">维修耗时</text>
      <text class="value">{{order.repairTime}}小时</text>
    </view>
    <view class="info-item">
      <text class="label">维修说明</text>
      <text class="value">{{order.repairDescription}}</text>
    </view>
    <view class="info-item" tt:if="{{order.completedAt}}">
      <text class="label">完成时间</text>
      <text class="value">{{order.completedAtFormatted}}</text>
    </view>
  </view>

  <!-- 耗材明细（仅已完成状态显示） -->
  <view class="card materials-detail" tt:if="{{order.status === 'completed' && order.materialsDetail && order.materialsDetail.length > 0}}">
    <view class="card-title">耗材明细</view>
    <view class="materials-list">
      <view class="material-item" tt:for="{{order.materialsDetail}}" tt:for-item="material" tt:key="index">
        <view class="material-info">
          <view class="material-name">{{material.name}}</view>
          <view class="material-spec" tt:if="{{material.specification}}">规格: {{material.specification}}</view>
        </view>
        <view class="material-price">
          <view class="quantity">×{{material.quantity}}</view>
          <view class="price">¥{{material.subtotal}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 费用明细（仅已完成状态显示） -->
  <view class="card fee-info" tt:if="{{order.status === 'completed'}}">
    <view class="card-title">费用明细</view>
    <view class="info-item" tt:if="{{order.laborFee && order.laborFee > 0}}">
      <text class="label">人工费</text>
      <text class="value">¥{{order.laborFee}}</text>
    </view>
    <view class="info-item" tt:if="{{order.serviceFee && order.serviceFee > 0}}">
      <text class="label">服务费</text>
      <text class="value">¥{{order.serviceFee}}</text>
    </view>
    <view class="info-item" tt:if="{{order.materialsFee && order.materialsFee > 0}}">
      <text class="label">耗材费</text>
      <text class="value">¥{{order.materialsFee}}</text>
    </view>
    <view class="info-item" tt:if="{{order.totalFee && order.totalFee > 0}}">
      <text class="label">总费用</text>
      <text class="value fee-total">¥{{order.totalFee}}</text>
    </view>
  </view>

  <view class="card remark-info" tt:if="{{order.remark}}">
    <view class="card-title">备注</view>
    <view class="info-item">
      <text class="value">{{order.remark}}</text>
    </view>
  </view>

  <view class="footer" tt:if="{{order.status === 'pending'}}">
    <button class="btn btn-default" bindtap="contactService">联系客服</button>
    <button class="btn btn-primary" bindtap="cancelOrder">取消订单</button>
  </view>

  <view class="footer" tt:elif="{{order.status !== 'completed'}}">
    <button class="btn btn-default" bindtap="contactService">联系客服</button>
  </view>

  <view class="footer" tt:else>
    <button class="btn btn-primary" bindtap="goToRepair">再次预约</button>
  </view>
</view>
