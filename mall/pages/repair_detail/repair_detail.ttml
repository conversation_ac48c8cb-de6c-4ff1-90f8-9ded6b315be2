<view class="container">
  <view class="status-bar status-{{order.status}}">
    <view class="status-icon">
      <view class="icon-wrapper">
        <image class="icon" src="/images/icons/订单.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="status-text">{{statusText[order.status]}}</view>
  </view>

  <view class="card order-info">
    <view class="card-title">订单信息</view>
    <view class="info-item">
      <text class="label">订单编号</text>
      <text class="value">{{order.orderNo}}</text>
    </view>
    <view class="info-item">
      <text class="label">创建时间</text>
      <text class="value">{{order.createdAtFormatted}}</text>
    </view>
    <view class="info-item">
      <text class="label">预约时间</text>
      <text class="value">{{order.appointmentTime}}</text>
    </view>
  </view>

  <view class="card fault-info">
    <view class="card-title">故障信息</view>
    <view class="info-item">
      <text class="label">故障类型</text>
      <text class="value">{{faultTypeMap[order.faultType] || '未知故障'}}</text>
    </view>
    <view class="info-item">
      <text class="label">充电桩型号</text>
      <text class="value">{{order.model}}</text>
    </view>
    <view class="info-item">
      <text class="label">故障描述</text>
      <text class="value">{{order.description}}</text>
    </view>
    <view class="info-item" tt:if="{{order.images && order.images.length > 0}}">
      <text class="label">故障图片</text>
      <view class="image-list">
        <image 
          tt:for="{{order.images}}" 
          tt:key="index" 
          class="fault-image" 
          src="{{item}}" 
          mode="aspectFill"
          bindtap="previewImage"
          data-url="{{item}}"
        ></image>
      </view>
    </view>
  </view>

  <view class="card service-info">
    <view class="card-title">服务信息</view>
    <view class="info-item">
      <text class="label">服务方式</text>
      <text class="value">{{order.serviceType === 'home' ? '上门维修' : '远程指导'}}</text>
    </view>
    <view class="info-item">
      <text class="label">联系人</text>
      <text class="value">{{order.name}}</text>
    </view>
    <view class="info-item">
      <text class="label">联系电话</text>
      <text class="value">{{order.phone}}</text>
    </view>
    <view class="info-item" tt:if="{{order.serviceType === 'home'}}">
      <text class="label">服务地址</text>
      <text class="value">{{order.fullAddress}}</text>
    </view>
  </view>

  <view class="card engineer-info" tt:if="{{order.status !== 'pending' && order.engineerName}}">
    <view class="card-title">工程师信息</view>
    <view class="engineer">
      <image class="engineer-avatar" src="{{order.engineerAvatar || '/images/icons/engineer.png'}}" mode="aspectFill"></image>
      <view class="engineer-detail">
        <view class="engineer-name">{{order.engineerName}}</view>
        <view class="engineer-title">充电桩维修工程师</view>
      </view>
      <view class="engineer-contact" tt:if="{{order.status !== 'completed'}}">
        <button class="contact-btn" bindtap="callEngineer" data-phone="{{order.engineerPhone}}">
          <image class="contact-icon" src="/images/icons/phone.png" mode="aspectFit"></image>
        </button>
      </view>
    </view>
  </view>

  <view class="card fee-info" tt:if="{{order.status === 'completed' && (order.repairFee > 0 || order.partsFee > 0)}}">
    <view class="card-title">费用信息</view>
    <view class="info-item" tt:if="{{order.repairFee > 0}}">
      <text class="label">维修费用</text>
      <text class="value">¥{{order.repairFee}}</text>
    </view>
    <view class="info-item" tt:if="{{order.partsFee > 0}}">
      <text class="label">配件费用</text>
      <text class="value">¥{{order.partsFee}}</text>
    </view>
    <view class="info-item" tt:if="{{order.totalFee > 0}}">
      <text class="label">总费用</text>
      <text class="value fee-total">¥{{order.totalFee}}</text>
    </view>
  </view>

  <view class="card remark-info" tt:if="{{order.remark}}">
    <view class="card-title">备注</view>
    <view class="info-item">
      <text class="value">{{order.remark}}</text>
    </view>
  </view>

  <view class="footer" tt:if="{{order.status === 'pending'}}">
    <button class="btn btn-default" bindtap="contactService">联系客服</button>
    <button class="btn btn-primary" bindtap="cancelOrder">取消订单</button>
  </view>
  
  <view class="footer" tt:elif="{{order.status !== 'completed'}}">
    <button class="btn btn-default" bindtap="contactService">联系客服</button>
  </view>
  
  <view class="footer" tt:else>
    <button class="btn btn-primary" bindtap="goToRepair">再次预约</button>
  </view>
</view>
