.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

.status-bar {
  background-color: #1890ff;
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.status-pending {
  background-color: #1890ff;
}

.status-accepted {
  background-color: #fa8c16;
}

.status-processing {
  background-color: #52c41a;
}

.status-completed {
  background-color: #8c8c8c;
}

.status-icon {
  margin-bottom: 10px;
}

.icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 36px;
  height: 36px;
}

.status-text {
  font-size: 18px;
  font-weight: 500;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  margin: 15px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.label {
  width: 80px;
  color: #999;
  font-size: 14px;
}

.value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

.fee-total {
  color: #f5222d;
  font-weight: 500;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.fault-image {
  width: 80px;
  height: 80px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

.engineer {
  display: flex;
  align-items: center;
}

.engineer-avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-right: 15px;
}

.engineer-detail {
  flex: 1;
}

.engineer-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.engineer-title {
  font-size: 12px;
  color: #999;
}

.engineer-contact {
  display: flex;
}

.contact-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
  padding: 0;
}

.contact-icon {
  width: 20px;
  height: 20px;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10px 15px;
  display: flex;
  justify-content: center;
  gap: 20rpx; /* 按钮间距 */
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.footer .btn {
  flex: 0 0 auto;
  min-width: 160rpx;      /* 原来是 200rpx，改小 */
  height: 80rpx;          /* 设置高度更小 */
  font-size: 30rpx;       /* 字体缩小一点 */
  padding: 0 20rpx;       /* 左右内边距 */
  line-height: 80rpx;     /* 垂直居中对齐 */
  border-radius: 12rpx;   /* 圆角小一些，更简洁 */
}

/* .btn {
  margin-left: 10px;
  font-size: 14px;
  padding: 8px 20px;
  border-radius: 4px;
} */

.btn-primary {
  background-color: #1890ff;
  color: #fff;
  border: none;
}

.btn-default {
  background-color: #fff;
  color: #666;
  border: 1px solid #d9d9d9;
}

/* 维修进度时间轴样式 */
.progress-timeline {
  padding: 20px 0;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 8px;
  top: 20px;
  width: 2px;
  height: calc(100% + 20px);
  background-color: #e9ecef;
}

.timeline-item.active:not(:last-child)::after {
  background-color: #007bff;
}

.timeline-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #e9ecef;
  margin-right: 15px;
  margin-top: 2px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.timeline-item.active .timeline-dot {
  background-color: #007bff;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.timeline-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 耗材明细样式 */
.materials-list {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.material-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.material-item:last-child {
  border-bottom: none;
}

.material-info {
  flex: 1;
}

.material-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 3px;
}

.material-spec {
  font-size: 12px;
  color: #666;
}

.material-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.quantity {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.price {
  font-size: 14px;
  color: #ff4d4f;
  font-weight: 500;
}
