const app = getApp()

Page({
  data: {
    orderId: '',
    order: {},
    openId: '',
    statusIcon: '/images/icons/pending.png', // 默认状态图标
    statusText: {
      'pending': '待接单',
      'accepted': '待上门',
      'processing': '维修中',
      'completed': '已完成'
    },
    faultTypeMap: {
      'no_charging': '无法充电',
      'slow_charging': '充电慢',
      'error_code': '报错代码',
      'port_damage': '接口损坏',
      'not_starting': '无法启动',
      'overheating': '过热',
      'display_issue': '显示故障',
      'other': '其他故障'
    }
  },

  onLoad: function (options) {
    console.log('Repair detail page loaded with options:', options);

    // 获取订单ID
    const orderId = options.id;
    if (!orderId) {
      tt.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    this.setData({
      orderId: orderId
    });

    // 获取openId
    this.getOpenId();
  },

  onShow: function() {
    // 如果已经有openId和orderId，则获取订单详情
    if (this.data.openId && this.data.orderId) {
      this.getOrderDetail();
    }
  },

  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
      this.getOrderDetail();
    } else {
      // 从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.openId) {
            this.setData({
              openId: res.data.openId
            });
            this.getOrderDetail();
          } else {
            // 尝试从openId存储中获取
            tt.getStorage({
              key: 'openId',
              success: (res) => {
                if (res.data) {
                  this.setData({
                    openId: res.data
                  });
                  this.getOrderDetail();
                } else {
                  this.showLoginTip();
                }
              },
              fail: () => {
                this.showLoginTip();
              }
            });
          }
        },
        fail: () => {
          // 尝试从openId存储中获取
          tt.getStorage({
            key: 'openId',
            success: (res) => {
              if (res.data) {
                this.setData({
                  openId: res.data
                });
                this.getOrderDetail();
              } else {
                this.showLoginTip();
              }
            },
            fail: () => {
              this.showLoginTip();
            }
          });
        }
      });
    }
  },
  
  // 显示登录提示
  showLoginTip: function() {
    // 如果没有openId，提示用户登录
    tt.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    // 跳转到用户中心页面
    tt.switchTab({
      url: '/pages/user_center/user_center'
    });
  },

  // 获取订单详情
  getOrderDetail: function() {
    if (!this.data.openId || !this.data.orderId) {
      console.error('获取订单详情失败：openId或orderId为空');
      return;
    }

    tt.showLoading({
      title: '加载中...'
    });

    const api = require('../../utils/api');
    api.getRepairOrderDetail(this.data.orderId, this.data.openId).then(res => {
      tt.hideLoading();
      
      if (res.success && res.order) {
        // 格式化创建时间
        const order = res.order;
        if (order.createdAt) {
          const createdDate = new Date(order.createdAt);
          order.createdAtFormatted = `${createdDate.getFullYear()}-${this.padZero(createdDate.getMonth() + 1)}-${this.padZero(createdDate.getDate())} ${this.padZero(createdDate.getHours())}:${this.padZero(createdDate.getMinutes())}`;
        } else {
          order.createdAtFormatted = '未知';
        }

        // 格式化更新时间
        if (order.updatedAt) {
          const updatedDate = new Date(order.updatedAt);
          order.updatedAtFormatted = `${updatedDate.getFullYear()}-${this.padZero(updatedDate.getMonth() + 1)}-${this.padZero(updatedDate.getDate())} ${this.padZero(updatedDate.getHours())}:${this.padZero(updatedDate.getMinutes())}`;
        }

        // 解析图片JSON字符串
        if (order.images && typeof order.images === 'string') {
          try {
            order.images = JSON.parse(order.images);
          } catch (e) {
            console.error('解析图片JSON失败:', e);
            order.images = [];
          }
        } else if (!order.images) {
          order.images = [];
        }

        // 计算总费用
        if (order.repairFee || order.partsFee) {
          const repairFee = parseFloat(order.repairFee) || 0;
          const partsFee = parseFloat(order.partsFee) || 0;
          order.totalFee = (repairFee + partsFee).toFixed(2);
        }

        // 根据订单状态设置状态图标
        let statusIcon = '/images/icons/pending.png'; // 默认图标
        switch (order.status) {
          case 'pending':
            statusIcon = '/images/icons/pending.png';
            break;
          case 'accepted':
            statusIcon = '/images/icons/accepted.png';
            break;
          case 'processing':
            statusIcon = '/images/icons/processing.png';
            break;
          case 'completed':
            statusIcon = '/images/icons/completed.png';
            break;
        }

        this.setData({
          order: order,
          statusIcon: statusIcon
        });
      } else {
        console.error('获取订单详情失败:', res.message);
        tt.showToast({
          title: '获取订单详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('获取订单详情请求失败:', err);
      tt.showToast({
        title: '获取订单详情失败',
        icon: 'none'
      });
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const images = this.data.order.images;

    tt.previewImage({
      current: url,
      urls: images
    });
  },

  // 拨打工程师电话
  callEngineer: function(e) {
    const phone = e.currentTarget.dataset.phone;
    
    if (!phone) {
      tt.showToast({
        title: '电话号码不能为空',
        icon: 'none'
      });
      return;
    }

    tt.makePhoneCall({
      phoneNumber: phone
    });
  },

  // 联系客服
  contactService: function() {
    // 跳转到客服页面
    tt.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 取消订单
  cancelOrder: function() {
    tt.showModal({
      title: '提示',
      content: '确定要取消该维修订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.doCancelOrder();
        }
      }
    });
  },

  // 执行取消订单
  doCancelOrder: function() {
    if (!this.data.openId || !this.data.orderId) {
      console.error('取消订单失败：openId或orderId为空');
      return;
    }

    tt.showLoading({
      title: '取消中...'
    });

    const api = require('../../utils/api');
    api.cancelRepairOrder(this.data.orderId, this.data.openId).then(res => {
      tt.hideLoading();
      
      if (res.success) {
        tt.showToast({
          title: '取消成功',
          icon: 'success'
        });
        
        // 刷新订单详情
        this.getOrderDetail();
      } else {
        console.error('取消订单失败:', res.message);
        tt.showToast({
          title: res.message || '取消失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('取消订单请求失败:', err);
      tt.showToast({
        title: '取消失败',
        icon: 'none'
      });
    });
  },

  // 去预约维修
  goToRepair: function() {
    tt.navigateTo({
      url: '/pages/repair_form/repair_form'
    });
  },

  // 数字补零
  padZero: function(num) {
    return num < 10 ? '0' + num : num;
  }
})
