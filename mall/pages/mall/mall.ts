const app = getApp()

Page({
  data: {
    currentCategory: 0, // 0表示全部
    sortType: 'default', // 排序方式：default, sales, price
    priceOrder: 'desc', // 价格排序：asc, desc
    hasMore: true, // 是否有更多商品
    page: 1, // 当前页码
    pageSize: 10, // 每页商品数量

    categories: [
      {
        id: 0,
        name: '全部',
        image: '/images/icons/全部.png',
        selectedImage: '/images/icons/全部_selected.png'
      },
      {
        id: 1,
        name: '充电枪',
        image: '/images/icons/充电枪.png',
        selectedImage: '/images/icons/充电枪_selected.png'
      },
      {
        id: 2,
        name: '线缆',
        image: '/images/icons/线缆.png',
        selectedImage: '/images/icons/线缆_selected.png'
      },
      {
        id: 3,
        name: '模块',
        image: '/images/icons/模块.png',
        selectedImage: '/images/icons/模块_selected.png'
      },
      {
        id: 4,
        name: '保护器',
        image: '/images/icons/保护器.png',
        selectedImage: '/images/icons/保护器_selected.png'
      },
      {
        id: 5,
        name: '配件',
        image: '/images/icons/配件.png',
        selectedImage: '/images/icons/配件_selected.png'
      },
      {
        id: 6,
        name: '工具',
        image: '/images/icons/工具.png',
        selectedImage: '/images/icons/工具_selected.png'
      }
    ],

    banners: [
      {
        id: 1,
        image: '/images/轮播图/轮播图4.png'
      },
      {
        id: 2,
        image: '/images/轮播图/轮播图3.png'
      }
    ],

    products: [
      {
        id: 1,
        name: '快充Type-C充电枪',
        price: 299.00,
        sales: 128,
        image: '/images/products/快充充电枪.png',
        category: 1
      },
      {
        id: 2,
        name: '5米加长型充电线缆',
        price: 199.00,
        sales: 85,
        image: '/images/products/充电线缆.png',
        category: 2
      },
      {
        id: 3,
        name: '充电桩控制模块',
        price: 499.00,
        sales: 56,
        image: '/images/products/充电控制模块.png',
        category: 3
      },
      {
        id: 4,
        name: '充电桩防水保护盒',
        price: 159.00,
        sales: 92,
        image: '/images/products/充电桩防水保护盒.png',
        category: 4
      },
      {
        id: 5,
        name: '充电桩散热风扇',
        price: 89.00,
        sales: 103,
        image: '/images/products/充电桩散热风扇.png',
        category: 5
      },
      {
        id: 6,
        name: '充电桩维修工具套装',
        price: 399.00,
        sales: 45,
        image: '/images/products/充电桩维修工具套装.png',
        category: 6
      }
    ]
  },

  onLoad: function () {
    console.log('Mall page loaded');
  },

  onShow: function () {
    // 页面显示时执行

    // 更新自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
      console.log('商城页更新tabBar成功');
    } else {
      console.log('商城页获取tabBar失败');
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重置数据
    this.setData({
      page: 1,
      hasMore: true
    });

    // 重新加载商品
    this.loadProducts();

    // 停止下拉刷新
    tt.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore) {
      this.loadMoreProducts();
    }
  },

  // 加载商品
  loadProducts: function() {
    // 模拟加载数据
    // 实际开发中应该调用API获取商品数据
    console.log('Loading products...');

    // 模拟网络请求
    tt.showLoading({
      title: '加载中'
    });

    setTimeout(() => {
      tt.hideLoading();

      // 根据分类筛选商品
      let filteredProducts = this.data.products;
      if (this.data.currentCategory !== 0) {
        filteredProducts = filteredProducts.filter(item => item.category === this.data.currentCategory);
      }

      // 根据排序方式排序
      this.sortProducts(filteredProducts);

      this.setData({
        products: filteredProducts,
        hasMore: false // 示例数据较少，设为false
      });
    }, 500);
  },

  // 加载更多商品
  loadMoreProducts: function() {
    // 模拟加载更多数据
    this.setData({
      page: this.data.page + 1
    });

    // 模拟网络请求
    tt.showLoading({
      title: '加载中'
    });

    setTimeout(() => {
      tt.hideLoading();

      // 示例中没有更多数据，实际开发中应该调用API获取更多商品
      this.setData({
        hasMore: false
      });
    }, 500);
  },

  // 排序商品
  sortProducts: function(products) {
    switch(this.data.sortType) {
      case 'sales':
        products.sort((a, b) => b.sales - a.sales);
        break;
      case 'price':
        if (this.data.priceOrder === 'asc') {
          products.sort((a, b) => a.price - b.price);
        } else {
          products.sort((a, b) => b.price - a.price);
        }
        break;
      default:
        // 默认排序，可以按照综合评分或其他因素
        break;
    }
    return products;
  },

  // 分类点击
  onCategoryTap: function(e) {
    const id = e.currentTarget.dataset.id;

    if (id !== this.data.currentCategory) {
      this.setData({
        currentCategory: id,
        page: 1,
        hasMore: true
      });

      this.loadProducts();
    }
  },

  // 排序方式点击
  onSortTap: function(e) {
    const type = e.currentTarget.dataset.type;

    if (type === this.data.sortType) {
      // 如果点击的是当前排序方式，且是价格排序，则切换升降序
      if (type === 'price') {
        this.setData({
          priceOrder: this.data.priceOrder === 'asc' ? 'desc' : 'asc'
        });
      }
    } else {
      // 切换排序方式
      this.setData({
        sortType: type,
        priceOrder: type === 'price' ? 'desc' : this.data.priceOrder
      });
    }

    // 重新排序商品
    let products = [...this.data.products];
    this.sortProducts(products);

    this.setData({
      products: products
    });
  },

  // 显示筛选
  showFilter: function() {
    tt.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    });
  },

  // 搜索框点击
  goToSearch: function() {
    tt.showToast({
      title: '搜索功能开发中',
      icon: 'none'
    });
  },

  // 跳转到产品详情页
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    tt.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  }
})
