<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <text class="iconfont icon-search"></text>
    <input type="text" placeholder="搜索充电桩配件" bindtap="goToSearch" />
  </view>

  <!-- 分类 -->
  <scroll-view class="category-list" scroll-x="true" enable-flex="true">
    <view class="category-item {{currentCategory === item.id ? 'active' : ''}}"
          tt:for="{{categories}}"
          tt:key="id"
          bindtap="onCategoryTap"
          data-id="{{item.id}}">
      <view class="category-icon">
        <image src="{{currentCategory === item.id ? item.selectedImage : item.image}}" mode="aspectFit" class="category-icon-image"></image>
      </view>
      <view class="category-name">{{item.name}}</view>
    </view>
  </scroll-view>

  <!-- Banner -->
  <swiper class="banner" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
    <swiper-item tt:for="{{banners}}" tt:key="index">
      <image src="{{item.image}}" mode="aspectFill" />
    </swiper-item>
  </swiper>

  <!-- 筛选 -->
  <view class="tabs">
    <view class="tab {{sortType === 'default' ? 'active' : ''}}" bindtap="onSortTap" data-type="default">综合</view>
    <view class="tab {{sortType === 'sales' ? 'active' : ''}}" bindtap="onSortTap" data-type="sales">销量</view>
    <view class="tab {{sortType === 'price' ? 'active' : ''}}" bindtap="onSortTap" data-type="price">
      价格
      <text class="iconfont {{priceOrder === 'asc' ? 'icon-arrow-up' : 'icon-arrow-down'}}"></text>
    </view>
    <view class="tab" bindtap="showFilter">
      筛选 <text class="iconfont icon-filter"></text>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="grid">
    <view class="product-card" tt:for="{{products}}" tt:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
      <image src="{{item.image}}" mode="aspectFill" />
      <view class="info">
        <view class="title">{{item.name}}</view>
        <view class="flex-between">
          <view class="price">¥{{item.price}}</view>
          <view class="text-xs text-light">已售 {{item.sales}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="loading-more" tt:if="{{hasMore}}">
    <text>正在加载更多...</text>
  </view>
  <view class="no-more" tt:else>
    <text>没有更多商品了</text>
  </view>
</view>

<!-- 自定义导航栏 -->
<nav-bar active="mall"></nav-bar>
