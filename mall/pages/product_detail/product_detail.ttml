<view class="container">
  <!-- 商品图片 -->
  <swiper class="product-image" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
    <swiper-item tt:for="{{product.images}}" tt:key="index">
      <image src="{{item}}" mode="aspectFill" />
    </swiper-item>
  </swiper>
  
  <!-- 商品信息 -->
  <view class="product-info">
    <view class="product-price">¥{{product.price}}</view>
    <view class="product-title">{{product.name}}</view>
    <view class="product-sales">销量: {{product.sales}} | 评价: {{product.reviews}}</view>
  </view>
  
  <!-- 规格选择 -->
  <view class="card">
    <view class="flex-between" bindtap="showSpecSelector">
      <view>规格</view>
      <view class="text-primary">
        {{selectedSpec ? selectedSpec : '选择'}} 
        <text class="iconfont icon-right"></text>
      </view>
    </view>
    <view class="divider"></view>
    <view class="flex-between">
      <view>服务</view>
      <view class="text-xs">
        <text class="badge badge-outline">包邮</text>
        <text class="badge badge-outline">7天无理由退换</text>
        <text class="badge badge-outline">质保1年</text>
      </view>
    </view>
  </view>
  
  <!-- 选项卡 -->
  <view class="tabs">
    <view class="tab {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">商品详情</view>
    <view class="tab {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">规格参数</view>
    <view class="tab {{currentTab === 2 ? 'active' : ''}}" bindtap="switchTab" data-tab="2">用户评价({{product.reviews}})</view>
  </view>
  
  <!-- 选项卡内容 -->
  <view class="tab-content" hidden="{{currentTab !== 0}}">
    <view class="text-center mb-3">
      <image src="{{product.detailImage}}" mode="widthFix" style="width: 100%;" />
    </view>
    <view class="detail-section">
      <view class="detail-title">产品特点</view>
      <view class="detail-item" tt:for="{{product.features}}" tt:key="index">
        {{index + 1}}. {{item}}
      </view>
    </view>
    
    <view class="detail-section">
      <view class="detail-title">适用车型</view>
      <view class="detail-text">{{product.compatibleCars}}</view>
    </view>
  </view>
  
  <view class="tab-content" hidden="{{currentTab !== 1}}">
    <view class="spec-table">
      <view class="spec-row" tt:for="{{product.specifications}}" tt:key="name">
        <view class="spec-name">{{item.name}}</view>
        <view class="spec-value">{{item.value}}</view>
      </view>
    </view>
  </view>
  
  <view class="tab-content" hidden="{{currentTab !== 2}}">
    <view class="review-item" tt:for="{{product.reviewList}}" tt:key="id">
      <view class="flex gap-3">
        <image src="{{item.avatar}}" class="avatar" mode="aspectFill" />
        <view>
          <view class="font-bold">{{item.name}}</view>
          <view class="rating">
            <text class="iconfont icon-star" tt:for="{{item.rating}}" tt:key="*this"></text>
          </view>
          <view class="text-xs text-light">{{item.date}}</view>
        </view>
      </view>
      <view class="mt-2">
        <text class="text-sm">{{item.content}}</text>
      </view>
      <view class="review-images" tt:if="{{item.images && item.images.length > 0}}">
        <image tt:for="{{item.images}}" tt:key="*this" tt:for-item="img" src="{{img}}" mode="aspectFill" class="review-image" bindtap="previewImage" data-url="{{img}}" data-urls="{{item.images}}" />
      </view>
    </view>
  </view>
  
  <!-- 底部操作栏 -->
  <view class="product-actions">
    <view class="action-btn" bindtap="goToHome">
      <text class="iconfont icon-home"></text>
      <text>首页</text>
    </view>
    <view class="action-btn" bindtap="goToCart">
      <text class="iconfont icon-shopping-cart"></text>
      <text>购物车</text>
      <view class="cart-badge" tt:if="{{cartCount > 0}}">{{cartCount}}</view>
    </view>
    <view class="action-btn add-to-cart" bindtap="addToCart">加入购物车</view>
    <view class="action-btn buy-now" bindtap="buyNow">立即购买</view>
  </view>
  
  <!-- 规格选择弹窗 -->
  <view class="spec-selector {{showSpec ? 'show' : ''}}" bindtap="hideSpecSelector">
    <view class="spec-content" catchtap="preventBubble">
      <view class="spec-header">
        <image src="{{product.image}}" class="spec-image" mode="aspectFill" />
        <view class="spec-info">
          <view class="product-price">¥{{product.price}}</view>
          <view class="text-sm">库存: {{product.stock}}件</view>
          <view class="text-sm">已选: {{selectedSpec ? selectedSpec : '请选择规格'}}</view>
        </view>
        <view class="close-btn" bindtap="hideSpecSelector">
          <text class="iconfont icon-close"></text>
        </view>
      </view>
      
      <view class="spec-body">
        <view class="spec-group">
          <view class="spec-group-title">规格</view>
          <view class="spec-options">
            <view class="spec-option {{selectedSpecId === item.id ? 'selected' : ''}}" 
                  tt:for="{{product.specs}}" 
                  tt:key="id" 
                  bindtap="selectSpec" 
                  data-id="{{item.id}}" 
                  data-name="{{item.name}}">
              {{item.name}}
            </view>
          </view>
        </view>
        
        <view class="spec-group">
          <view class="spec-group-title">数量</view>
          <view class="quantity-selector">
            <view class="quantity-btn" bindtap="decreaseQuantity">-</view>
            <input type="number" class="quantity-input" value="{{quantity}}" bindinput="onQuantityInput" />
            <view class="quantity-btn" bindtap="increaseQuantity">+</view>
          </view>
        </view>
      </view>
      
      <view class="spec-footer">
        <button class="btn btn-block" bindtap="confirmSpec">确定</button>
      </view>
    </view>
  </view>
</view>
