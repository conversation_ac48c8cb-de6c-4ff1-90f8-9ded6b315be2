const app = getApp()
const api = require('../../utils/api.js')

Page({
  data: {
    id: null,
    product: null,
    reviews: [],
    reviewCount: 0,
    avgRating: 0,
    currentTab: 0,
    showSpec: false,
    selectedSpecId: null,
    selectedSpec: '',
    quantity: 1,
    cartCount: 0
  },

  onLoad: function (options) {
    const id = parseInt(options.id);
    this.setData({
      id: id
    });

    // 获取商品详情
    this.loadProductDetail(id);

    // 获取购物车数量
    this.getCartCount();
  },

  onShow: function () {
    // 页面显示时更新购物车数量
    this.getCartCount();
  },

  // 加载商品详情
  loadProductDetail: function(id) {
    tt.showLoading({
      title: '加载中'
    });

    api.getProductDetail(id).then(res => {
      tt.hideLoading();

      if (res.success) {
        const productData = res.data;

        // 解析JSON字符串字段
        let product = productData.product;
        if (product.images && typeof product.images === 'string') {
          try {
            product.images = JSON.parse(product.images);
          } catch (e) {
            product.images = [];
          }
        }

        if (product.features && typeof product.features === 'string') {
          try {
            product.features = JSON.parse(product.features);
          } catch (e) {
            product.features = [];
          }
        }

        if (product.specifications && typeof product.specifications === 'string') {
          try {
            product.specifications = JSON.parse(product.specifications);
          } catch (e) {
            product.specifications = [];
          }
        }

        if (product.specs && typeof product.specs === 'string') {
          try {
            product.specs = JSON.parse(product.specs);
          } catch (e) {
            product.specs = [];
          }
        }

        if (product.services && typeof product.services === 'string') {
          try {
            product.services = JSON.parse(product.services);
          } catch (e) {
            product.services = [];
          }
        }

        // 处理评价数据
        let reviews = productData.reviews || [];
        reviews.forEach(review => {
          if (review.images && typeof review.images === 'string') {
            try {
              review.images = JSON.parse(review.images);
            } catch (e) {
              review.images = [];
            }
          }
          // 格式化日期
          if (review.createdAt) {
            review.date = new Date(review.createdAt).toLocaleDateString();
          }
          // 使用用户昵称作为显示名称
          review.name = review.userNickname || '匿名用户';
          review.avatar = review.userAvatar || 'https://randomuser.me/api/portraits/lego/1.jpg';
        });

        this.setData({
          product: product,
          reviews: reviews,
          reviewCount: productData.reviewCount || 0,
          avgRating: productData.avgRating || 0
        });
      } else {
        tt.showToast({
          title: res.message || '商品不存在',
          icon: 'none'
        });

        setTimeout(() => {
          tt.navigateBack();
        }, 1500);
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('获取商品详情失败:', err);
      tt.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });

      setTimeout(() => {
        tt.navigateBack();
      }, 1500);
    });
  },

  // 获取购物车数量
  getCartCount: function() {
    const cartItems = app.globalData.cartItems || [];
    const count = cartItems.reduce((total, item) => total + item.quantity, 0);

    this.setData({
      cartCount: count
    });
  },

  // 切换选项卡
  switchTab: function(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({
      currentTab: tab
    });
  },

  // 显示规格选择器
  showSpecSelector: function() {
    this.setData({
      showSpec: true
    });
  },

  // 隐藏规格选择器
  hideSpecSelector: function() {
    this.setData({
      showSpec: false
    });
  },

  // 防止冒泡
  preventBubble: function() {
    return;
  },

  // 选择规格
  selectSpec: function(e) {
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;

    this.setData({
      selectedSpecId: id,
      selectedSpec: name
    });
  },

  // 减少数量
  decreaseQuantity: function() {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      });
    }
  },

  // 增加数量
  increaseQuantity: function() {
    if (this.data.quantity < this.data.product.stock) {
      this.setData({
        quantity: this.data.quantity + 1
      });
    } else {
      tt.showToast({
        title: '已达到最大库存',
        icon: 'none'
      });
    }
  },

  // 输入数量
  onQuantityInput: function(e) {
    let value = parseInt(e.detail.value);

    if (isNaN(value) || value < 1) {
      value = 1;
    } else if (value > this.data.product.stock) {
      value = this.data.product.stock;
      tt.showToast({
        title: '已达到最大库存',
        icon: 'none'
      });
    }

    this.setData({
      quantity: value
    });
  },

  // 确认规格选择
  confirmSpec: function() {
    if (!this.data.selectedSpecId) {
      tt.showToast({
        title: '请选择规格',
        icon: 'none'
      });
      return;
    }

    this.hideSpecSelector();
  },

  // 添加到购物车
  addToCart: function() {
    if (!this.data.selectedSpecId) {
      this.showSpecSelector();
      return;
    }

    const product = this.data.product;
    const selectedSpec = this.data.selectedSpec;
    const quantity = this.data.quantity;

    // 构建购物车商品对象
    const cartItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      spec: selectedSpec,
      quantity: quantity
    };

    // 调用全局方法添加到购物车
    app.addToCart(cartItem);

    // 更新购物车数量
    this.getCartCount();

    tt.showToast({
      title: '已加入购物车',
      icon: 'success'
    });
  },

  // 立即购买
  buyNow: function() {
    if (!this.data.selectedSpecId) {
      this.showSpecSelector();
      return;
    }

    const product = this.data.product;
    const selectedSpec = this.data.selectedSpec;
    const quantity = this.data.quantity;

    // 构建购物车商品对象
    const cartItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      spec: selectedSpec,
      quantity: quantity
    };

    // 调用全局方法添加到购物车
    app.addToCart(cartItem);

    // 跳转到确认订单页面
    tt.navigateTo({
      url: '/pages/order_confirm/order_confirm'
    });
  },

  // 跳转到首页
  goToHome: function() {
    // 使用reLaunch方法跳转到首页，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/index/index',
      success: () => {
        console.log('跳转到首页成功');
      },
      fail: (err) => {
        console.error('跳转到首页失败:', err);
      }
    });
  },

  // 跳转到购物车
  goToCart: function() {
    tt.navigateTo({
      url: '/pages/cart/cart'
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;

    tt.previewImage({
      current: url,
      urls: urls
    });
  }
})
