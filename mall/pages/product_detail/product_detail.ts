const app = getApp()

Page({
  data: {
    id: null,
    product: null,
    currentTab: 0,
    showSpec: false,
    selectedSpecId: null,
    selectedSpec: '',
    quantity: 1,
    cartCount: 0,

    // 示例数据
    productData: {
      1: {
        id: 1,
        name: '快充Type-C充电枪 60kW大功率 兼容多种车型',
        price: 299.00,
        sales: 128,
        reviews: 45,
        stock: 999,
        image: '/images/products/快充充电枪.png',
        images: [
          '/images/products/快充充电枪.png',
          '/images/products/快充枪2.png'
        ],
        detailImage: '/images/products/快充充电枪.png',
        features: [
          '大功率快充：支持60kW大功率充电，充电速度提升50%',
          '兼容性强：适配市面上95%以上的电动汽车型号',
          '安全保障：多重保护机制，防过充、防过热、防短路',
          '耐用设计：采用高强度材料，使用寿命长达5年以上',
          '智能识别：自动识别车辆充电需求，优化充电效率'
        ],
        compatibleCars: '特斯拉、比亚迪、蔚来、小鹏、理想、宝马、奔驰等主流电动汽车品牌',
        specifications: [
          { name: '型号', value: 'TC-60K' },
          { name: '输入电压', value: 'AC 220V' },
          { name: '输出功率', value: '60kW' },
          { name: '充电接口', value: 'Type-C' },
          { name: '线缆长度', value: '3米' },
          { name: '工作温度', value: '-20℃~60℃' },
          { name: '防护等级', value: 'IP65' },
          { name: '产品尺寸', value: '120×80×50mm' },
          { name: '产品重量', value: '1.2kg' }
        ],
        specs: [
          { id: 1, name: '标准版' },
          { id: 2, name: '豪华版' },
          { id: 3, name: '专业版' }
        ],
        reviewList: [
          {
            id: 1,
            name: '张先生',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
            rating: 5,
            date: '2023-05-10',
            content: '质量非常好，充电速度快，兼容性也很强，推荐购买！',
            images: [
              '/images/products/快充充电枪.png'
            ]
          },
          {
            id: 2,
            name: '李女士',
            avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
            rating: 4,
            date: '2023-05-05',
            content: '充电速度确实快，但是价格稍微有点贵，总体还是很满意的。',
            images: []
          }
        ]
      },
      2: {
        id: 2,
        name: '5米加长型充电线缆',
        price: 199.00,
        sales: 85,
        reviews: 32,
        stock: 500,
        image: 'https://images.unsplash.com/photo-1601814933824-fd0b574dd592?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        images: [
          'https://images.unsplash.com/photo-1601814933824-fd0b574dd592?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
        ],
        detailImage: 'https://images.unsplash.com/photo-1601814933824-fd0b574dd592?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
        features: [
          '加长设计：5米长度，满足各种充电场景需求',
          '高品质材料：采用优质铜芯，传输效率高',
          '耐磨耐用：外层采用PVC材质，防水防晒',
          '安全保障：多重保护，防过热、防短路',
          '兼容性强：适配市面上主流充电桩和电动车型'
        ],
        compatibleCars: '适用于所有标准充电接口的电动汽车',
        specifications: [
          { name: '型号', value: 'CL-5M' },
          { name: '长度', value: '5米' },
          { name: '材质', value: '铜芯+PVC外层' },
          { name: '最大电流', value: '32A' },
          { name: '工作温度', value: '-30℃~80℃' },
          { name: '防护等级', value: 'IP67' },
          { name: '产品重量', value: '2.5kg' }
        ],
        specs: [
          { id: 1, name: '3米' },
          { id: 2, name: '5米' },
          { id: 3, name: '8米' }
        ],
        reviewList: [
          {
            id: 1,
            name: '王先生',
            avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
            rating: 5,
            date: '2023-05-12',
            content: '线缆质量很好，长度刚好满足我的需求，充电稳定，没有发热现象。',
            images: []
          },
          {
            id: 2,
            name: '赵女士',
            avatar: 'https://randomuser.me/api/portraits/women/24.jpg',
            rating: 4,
            date: '2023-05-08',
            content: '线缆不错，但是有点重，携带不太方便，其他都很满意。',
            images: []
          }
        ]
      }
    }
  },

  onLoad: function (options) {
    const id = parseInt(options.id);
    this.setData({
      id: id
    });

    // 获取商品详情
    this.loadProductDetail(id);

    // 获取购物车数量
    this.getCartCount();
  },

  onShow: function () {
    // 页面显示时更新购物车数量
    this.getCartCount();
  },

  // 加载商品详情
  loadProductDetail: function(id) {
    // 模拟加载数据
    // 实际开发中应该调用API获取商品详情
    const product = this.data.productData[id];

    if (product) {
      this.setData({
        product: product
      });
    } else {
      tt.showToast({
        title: '商品不存在',
        icon: 'none'
      });

      setTimeout(() => {
        tt.navigateBack();
      }, 1500);
    }
  },

  // 获取购物车数量
  getCartCount: function() {
    const cartItems = app.globalData.cartItems || [];
    const count = cartItems.reduce((total, item) => total + item.quantity, 0);

    this.setData({
      cartCount: count
    });
  },

  // 切换选项卡
  switchTab: function(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({
      currentTab: tab
    });
  },

  // 显示规格选择器
  showSpecSelector: function() {
    this.setData({
      showSpec: true
    });
  },

  // 隐藏规格选择器
  hideSpecSelector: function() {
    this.setData({
      showSpec: false
    });
  },

  // 防止冒泡
  preventBubble: function() {
    return;
  },

  // 选择规格
  selectSpec: function(e) {
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;

    this.setData({
      selectedSpecId: id,
      selectedSpec: name
    });
  },

  // 减少数量
  decreaseQuantity: function() {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      });
    }
  },

  // 增加数量
  increaseQuantity: function() {
    if (this.data.quantity < this.data.product.stock) {
      this.setData({
        quantity: this.data.quantity + 1
      });
    } else {
      tt.showToast({
        title: '已达到最大库存',
        icon: 'none'
      });
    }
  },

  // 输入数量
  onQuantityInput: function(e) {
    let value = parseInt(e.detail.value);

    if (isNaN(value) || value < 1) {
      value = 1;
    } else if (value > this.data.product.stock) {
      value = this.data.product.stock;
      tt.showToast({
        title: '已达到最大库存',
        icon: 'none'
      });
    }

    this.setData({
      quantity: value
    });
  },

  // 确认规格选择
  confirmSpec: function() {
    if (!this.data.selectedSpecId) {
      tt.showToast({
        title: '请选择规格',
        icon: 'none'
      });
      return;
    }

    this.hideSpecSelector();
  },

  // 添加到购物车
  addToCart: function() {
    if (!this.data.selectedSpecId) {
      this.showSpecSelector();
      return;
    }

    const product = this.data.product;
    const selectedSpec = this.data.selectedSpec;
    const quantity = this.data.quantity;

    // 构建购物车商品对象
    const cartItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      spec: selectedSpec,
      quantity: quantity
    };

    // 调用全局方法添加到购物车
    app.addToCart(cartItem);

    // 更新购物车数量
    this.getCartCount();

    tt.showToast({
      title: '已加入购物车',
      icon: 'success'
    });
  },

  // 立即购买
  buyNow: function() {
    if (!this.data.selectedSpecId) {
      this.showSpecSelector();
      return;
    }

    const product = this.data.product;
    const selectedSpec = this.data.selectedSpec;
    const quantity = this.data.quantity;

    // 构建购物车商品对象
    const cartItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      spec: selectedSpec,
      quantity: quantity
    };

    // 调用全局方法添加到购物车
    app.addToCart(cartItem);

    // 跳转到确认订单页面
    tt.navigateTo({
      url: '/pages/order_confirm/order_confirm'
    });
  },

  // 跳转到首页
  goToHome: function() {
    // 使用reLaunch方法跳转到首页，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/index/index',
      success: () => {
        console.log('跳转到首页成功');
      },
      fail: (err) => {
        console.error('跳转到首页失败:', err);
      }
    });
  },

  // 跳转到购物车
  goToCart: function() {
    tt.navigateTo({
      url: '/pages/cart/cart'
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;

    tt.previewImage({
      current: url,
      urls: urls
    });
  }
})
