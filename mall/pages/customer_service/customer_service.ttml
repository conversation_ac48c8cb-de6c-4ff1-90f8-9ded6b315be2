<view class="container">
  <!-- 客服头部 -->
  <view class="service-header">
    <view class="service-title">充电桩维修客服</view>
    <view class="service-status">
      <view class="status-dot online"></view>
      <text>在线</text>
    </view>
  </view>
  
  <!-- 聊天区域 -->
  <scroll-view class="chat-container" scroll-y="true" scroll-into-view="{{scrollToMessage}}" scroll-with-animation="true">
    <view class="chat-timeline">今天 {{currentTime}}</view>
    
    <!-- 系统消息 -->
    <view class="system-message">
      <text>您已接入在线客服，请问有什么可以帮助您？</text>
    </view>
    
    <!-- 客服消息 -->
    <view class="message service-message">
      <image class="avatar" src="/images/icons/客服.png" mode="aspectFill"></image>
      <view class="message-content">
        <view class="message-bubble">
          您好，我是充电桩维修专员小蓝，很高兴为您服务。请问您需要咨询什么问题？
        </view>
        
      </view>
    </view>
    <view class="message-time">{{currentTime}}</view>
    <!-- 用户消息 -->
    <view tt:for="{{messages}}" tt:key="id" class="message {{item.type === 'user' ? 'user-message' : 'service-message'}}" id="msg-{{item.id}}">
      <image tt:if="{{item.type === 'service'}}" class="avatar" src="/images/icons/客服.png" mode="aspectFill"></image>
      <view class="message-content">
        <view class="message-bubble">{{item.content}}</view>
        
      </view>
      <image tt:if="{{item.type === 'user'}}" class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      
    </view>
    <view class="message-time">{{item.time}}</view>
    
  </scroll-view>
  
  <!-- 快捷问题 -->
  <scroll-view class="quick-questions" scroll-x="true" enable-flex="true">
    <view class="question-item" tt:for="{{quickQuestions}}" tt:key="*this" bindtap="sendQuickQuestion" data-question="{{item}}">
      {{item}}
    </view>
  </scroll-view>
  
  <!-- 输入区域 -->
  <view class="input-container">
    <view class="input-box">
      <input type="text" value="{{inputMessage}}" bindinput="onInputChange" placeholder="请输入您的问题" confirm-type="send" bindconfirm="sendMessage" />
      <view class="send-btn {{inputMessage ? 'active' : ''}}" bindtap="sendMessage">发送</view>
    </view>
    <view class="input-tools">
      <view class="tool-item" bindtap="chooseImage">
        <image src="/images/icons/image.png" mode="aspectFit"></image>
      </view>
      <view class="tool-item" bindtap="makePhoneCall">
        <image src="/images/icons/phone.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
</view>
