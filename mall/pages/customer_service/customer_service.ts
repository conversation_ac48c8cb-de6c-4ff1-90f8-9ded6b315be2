const app = getApp()

Page({
  data: {
    userInfo: null,
    currentTime: '',
    inputMessage: '',
    scrollToMessage: '',
    messages: [],
    quickQuestions: [
      '充电桩无法充电怎么办？',
      '如何预约上门维修？',
      '维修费用是多少？',
      '有哪些常见故障？',
      '保修期是多久？'
    ]
  },
  
  onLoad: function () {
    // 获取当前时间
    this.updateCurrentTime();
    
    // 获取用户信息
    this.getUserInfo();
  },
  
  onShow: function () {
    // 页面显示时更新时间
    this.updateCurrentTime();
  },
  
  // 更新当前时间
  updateCurrentTime: function() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    
    this.setData({
      currentTime: `${hours}:${minutes}`
    });
  },
  
  // 获取用户信息
  getUserInfo: function() {
    const userInfo = app.globalData.userInfo;
    
    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    } else {
      // 如果没有用户信息，尝试获取
      tt.getUserInfo({
        success: (res) => {
          app.globalData.userInfo = res.userInfo;
          this.setData({
            userInfo: res.userInfo
          });
        }
      });
    }
  },
  
  // 输入框内容变化
  onInputChange: function(e) {
    this.setData({
      inputMessage: e.detail.value
    });
  },
  
  // 发送消息
  sendMessage: function() {
    const message = this.data.inputMessage.trim();
    
    if (!message) {
      return;
    }
    
    // 添加用户消息
    this.addMessage('user', message);
    
    // 清空输入框
    this.setData({
      inputMessage: ''
    });
    
    // 模拟客服回复
    this.simulateServiceReply(message);
  },
  
  // 发送快捷问题
  sendQuickQuestion: function(e) {
    const question = e.currentTarget.dataset.question;
    
    // 设置到输入框
    this.setData({
      inputMessage: question
    });
    
    // 发送消息
    this.sendMessage();
  },
  
  // 添加消息
  addMessage: function(type, content) {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const time = `${hours}:${minutes}`;
    
    const messages = this.data.messages;
    const id = messages.length + 1;
    
    messages.push({
      id: id,
      type: type,
      content: content,
      time: time
    });
    
    this.setData({
      messages: messages,
      scrollToMessage: `msg-${id}`
    });
  },
  
  // 模拟客服回复
  simulateServiceReply: function(userMessage) {
    // 延迟回复，模拟真实客服
    setTimeout(() => {
      let reply = '';
      
      // 根据用户消息内容生成回复
      if (userMessage.includes('充电') && userMessage.includes('无法')) {
        reply = '充电桩无法充电可能有多种原因，常见的有：1. 充电枪连接不良；2. 充电桩电源问题；3. 车辆充电口故障。建议您先检查充电枪是否连接牢固，如果问题依然存在，可以预约我们的上门维修服务。';
      } else if (userMessage.includes('预约') || userMessage.includes('上门')) {
        reply = '您可以通过我们的小程序"维修"页面预约上门维修服务，选择故障类型、填写地址和预约时间即可。我们的技师会在预约时间上门为您解决问题。';
      } else if (userMessage.includes('费用') || userMessage.includes('价格')) {
        reply = '我们的维修费用根据故障类型和维修难度而定，基础检测费为50元，如果确认维修则检测费减免。常见故障维修费用在100-500元之间，具体价格会在技师上门检测后告知您，您确认后我们才会进行维修。';
      } else if (userMessage.includes('故障')) {
        reply = '充电桩常见故障包括：充电枪损坏、线缆老化、控制模块故障、通信异常、过热保护等。我们的技师都经过专业培训，能够处理各类充电桩故障。';
      } else if (userMessage.includes('保修')) {
        reply = '我们的充电桩产品保修期为1年，维修服务保修期为3个月。在保修期内，因产品质量问题导致的故障我们提供免费维修服务。人为损坏、不当使用或超出保修期的故障需要收取维修费用。';
      } else {
        reply = '感谢您的咨询。如果您有充电桩相关的问题，可以直接描述您遇到的情况，我们的专业技术人员会为您提供解决方案。您也可以拨打我们的客服热线：************，获取更多帮助。';
      }
      
      // 添加客服回复
      this.addMessage('service', reply);
    }, 1000);
  },
  
  // 选择图片
  chooseImage: function() {
    tt.chooseImage({
      count: 1,
      success: (res) => {
        tt.showToast({
          title: '图片发送功能开发中',
          icon: 'none'
        });
      }
    });
  },
  
  // 拨打电话
  makePhoneCall: function() {
    tt.makePhoneCall({
      phoneNumber: '************',
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败', err);
      }
    });
  }
})
