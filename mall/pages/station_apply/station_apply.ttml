<scroll-view class="container" scroll-y="true">
  <!-- 申请须知 -->
  <view class="notice">
    <view class="notice-title">
      <text class="iconfont icon-info-circle"></text>
      <text>申请须知</text>
    </view>
    <view class="notice-content">
      <text>• 请确保提供的信息真实有效</text>
      <text>• 营业执照和资质证书必须清晰可见</text>
      <text>• 审核周期为3-5个工作日</text>
      <text>• 如有疑问请联系客服：400-888-8888</text>
    </view>
  </view>

  <!-- 基本信息 -->
  <view class="form-section">
    <view class="section-title">基本信息</view>

    <view class="form-item">
      <view class="form-label">网点名称 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入网点名称"
        value="{{formData.name}}"
        data-field="name"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">联系人 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入联系人姓名"
        value="{{formData.contactPerson}}"
        data-field="contactPerson"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">联系电话 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入联系电话"
        type="number"
        value="{{formData.phone}}"
        data-field="phone"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">邮箱地址</view>
      <input
        class="form-input"
        placeholder="请输入邮箱地址"
        value="{{formData.email}}"
        data-field="email"
        bindinput="onInputChange"
      />
    </view>
  </view>

  <!-- 地址信息 -->
  <view class="form-section">
    <view class="section-title">地址信息</view>

    <view class="form-item">
      <view class="form-label">网点地址 <text class="required">*</text></view>
      <view class="location-picker" bindtap="chooseLocation">
        <text class="location-text {{formData.address ? '' : 'placeholder'}}">{{formData.address || '点击选择网点地址'}}</text>
        <text class="iconfont icon-location"></text>
      </view>
    </view>

    <view class="form-item">
      <view class="form-label">营业时间</view>
      <input
        class="form-input"
        placeholder="如：09:00-18:00"
        value="{{formData.businessHours}}"
        data-field="businessHours"
        bindinput="onInputChange"
      />
    </view>
  </view>

  <!-- 服务信息 -->
  <view class="form-section">
    <view class="section-title">服务信息</view>

    <view class="form-item">
      <view class="form-label">服务类型 <text class="required">*</text></view>
      <view class="checkbox-group">
        <view
          class="checkbox-item {{item.checked ? 'checked' : ''}}"
          tt:for="{{serviceTypeOptions}}"
          tt:key="id"
          bindtap="onServiceTypeChange"
          data-index="{{index}}"
        >
          <text class="checkbox-icon">{{item.checked ? '✓' : ''}}</text>
          <text class="checkbox-text">{{item.name}}</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-label">支持设备类型</view>
      <view class="checkbox-group">
        <view
          class="checkbox-item {{item.checked ? 'checked' : ''}}"
          tt:for="{{equipmentTypeOptions}}"
          tt:key="id"
          bindtap="onEquipmentTypeChange"
          data-index="{{index}}"
        >
          <text class="checkbox-icon">{{item.checked ? '✓' : ''}}</text>
          <text class="checkbox-text">{{item.name}}</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-label">服务描述</view>
      <textarea
        class="form-textarea"
        placeholder="请简要描述您的服务特色和优势"
        value="{{formData.serviceDescription}}"
        data-field="serviceDescription"
        bindinput="onInputChange"
        maxlength="500"
      ></textarea>
    </view>
  </view>

  <!-- 设施设备 -->
  <view class="form-section">
    <view class="section-title">设施设备</view>

    <view class="form-item">
      <view class="form-label">拥有设施</view>
      <view class="checkbox-group">
        <view
          class="checkbox-item {{item.checked ? 'checked' : ''}}"
          tt:for="{{facilityOptions}}"
          tt:key="id"
          bindtap="onFacilityChange"
          data-index="{{index}}"
        >
          <text class="checkbox-icon">{{item.checked ? '✓' : ''}}</text>
          <text class="checkbox-text">{{item.name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 费用信息 -->
  <view class="form-section">
    <view class="section-title">费用信息</view>

    <view class="form-item">
      <view class="form-label">基础服务费（元）</view>
      <input
        class="form-input"
        placeholder="请输入基础服务费"
        type="digit"
        value="{{formData.serviceFee}}"
        data-field="serviceFee"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">检测费用（元）</view>
      <input
        class="form-input"
        placeholder="请输入检测费用"
        type="digit"
        value="{{formData.inspectionFee}}"
        data-field="inspectionFee"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">停车信息</view>
      <input
        class="form-input"
        placeholder="如：免费停车，可容纳20辆车"
        value="{{formData.parkingInfo}}"
        data-field="parkingInfo"
        bindinput="onInputChange"
      />
    </view>
  </view>

  <!-- 资质证明 -->
  <view class="form-section">
    <view class="section-title">资质证明</view>

    <view class="form-item">
      <view class="form-label">营业执照 <text class="required">*</text></view>
      <view class="upload-area" bindtap="uploadBusinessLicense">
        <image
          tt:if="{{formData.businessLicense}}"
          src="{{formData.businessLicense}}"
          class="uploaded-image"
          mode="aspectFill"
        ></image>
        <view tt:else class="upload-placeholder">
          <text class="iconfont icon-camera"></text>
          <text>点击上传营业执照</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-label">资质证书</view>
      <view class="upload-grid">
        <view
          class="upload-item"
          tt:for="{{formData.qualificationCertificates}}"
          tt:key="*this"
        >
          <image src="{{item}}" class="uploaded-image" mode="aspectFill"></image>
          <view class="delete-btn" bindtap="deleteCertificate" data-index="{{index}}">
            <text class="iconfont icon-close"></text>
          </view>
        </view>
        <view
          class="upload-item upload-placeholder"
          bindtap="uploadCertificates"
          tt:if="{{formData.qualificationCertificates.length < 9}}"
        >
          <text class="iconfont icon-plus"></text>
          <text>添加证书</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 网点图片 -->
  <view class="form-section">
    <view class="section-title">网点图片</view>

    <view class="form-item">
      <view class="form-label">网点照片</view>
      <view class="upload-grid">
        <view
          class="upload-item"
          tt:for="{{formData.images}}"
          tt:key="*this"
        >
          <image src="{{item}}" class="uploaded-image" mode="aspectFill"></image>
          <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">
            <text class="iconfont icon-close"></text>
          </view>
        </view>
        <view
          class="upload-item upload-placeholder"
          bindtap="uploadImages"
          tt:if="{{formData.images.length < 9}}"
        >
          <text class="iconfont icon-plus"></text>
          <text>添加图片</text>
        </view>
      </view>
      <view class="upload-tip">建议上传网点外观、内部环境、设备设施等照片</view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button
      class="submit-btn {{submitting ? 'submitting' : ''}}"
      bindtap="submitApplication"
      disabled="{{submitting}}"
    >
      {{submitting ? '提交中...' : '提交申请'}}
    </button>
    <view class="submit-tip">
      提交后我们将在3-5个工作日内完成审核，请保持电话畅通
    </view>
  </view>
</scroll-view>
