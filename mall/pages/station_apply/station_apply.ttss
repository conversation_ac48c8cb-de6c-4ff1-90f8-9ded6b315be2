page {
  background-color: #f5f5f5;
}

.container {
  height: 100vh;
  padding-bottom: 200rpx;
}

/* 申请须知 */
.notice {
  background-color: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 12rpx;
  margin: 20rpx;
  padding: 20rpx;
}

.notice-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #856404;
  margin-bottom: 15rpx;
}

.notice-title .iconfont {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.notice-content text {
  font-size: 24rpx;
  color: #856404;
  line-height: 1.4;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #1e88e5;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.form-input:focus {
  border-color: #1e88e5;
  background-color: #fff;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.form-textarea:focus {
  border-color: #1e88e5;
  background-color: #fff;
}

/* 位置选择器 */
.location-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.location-text.placeholder {
  color: #999;
}

.location-picker .iconfont {
  color: #1e88e5;
  font-size: 32rpx;
}

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.checkbox-item.checked {
  background-color: #e3f2fd;
  border-color: #1e88e5;
  color: #1e88e5;
}

.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #fff;
  background-color: #fff;
}

.checkbox-item.checked .checkbox-icon {
  background-color: #1e88e5;
  border-color: #1e88e5;
}

/* 上传区域 */
.upload-area {
  width: 100%;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.upload-placeholder .iconfont {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.upload-placeholder text:last-child {
  font-size: 24rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

/* 上传网格 */
.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

.upload-item {
  position: relative;
  width: 100%;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.upload-item.upload-placeholder {
  border: 2rpx dashed #ddd;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-item.upload-placeholder .iconfont {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.upload-item.upload-placeholder text:last-child {
  font-size: 22rpx;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
}

.upload-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
}

/* 提交区域 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
  z-index: 100;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #1e88e5;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.submitting {
  background-color: #999;
}

.submit-btn::after {
  border: none;
}

.submit-tip {
  text-align: center;
  font-size: 22rpx;
  color: #999;
  margin-top: 15rpx;
  line-height: 1.4;
}
