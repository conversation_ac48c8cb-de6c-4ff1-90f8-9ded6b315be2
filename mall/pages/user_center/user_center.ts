const app = getApp()

Page({
  data: {
    userInfo: null,
    orderStats: {
      pendingPayment: 0,
      pendingDelivery: 0,
      pendingReceipt: 0,
      completed: 0
    },
    repairStats: {
      pending: 0,
      accepted: 0,
      processing: 0,
      completed: 0
    },
    unreadMessages: 0
  },

  onLoad: function () {
    console.log('User center page loaded');
  },

  onShow: function () {
    // 获取用户信息
    this.getUserInfo();

    // 获取订单统计
    this.getOrderStats();

    // 获取维修订单统计
    this.getRepairStats();

    // 更新自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      });
      console.log('用户中心页更新tabBar成功');
    } else {
      console.log('用户中心页获取tabBar失败');
    }
  },

  // 获取用户信息
  getUserInfo: function() {
    const userInfo = app.globalData.userInfo;
    const userId = app.globalData.userId;

    if (userInfo && userId) {
      console.log('从全局数据获取用户信息:', userInfo);
      this.setData({
        userInfo: userInfo
      });
    } else {
      // 如果全局数据中没有用户信息，尝试从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.userInfo) {
            console.log('从本地存储获取用户信息:', res.data.userInfo);
            this.setData({
              userInfo: res.data.userInfo
            });
          }
        }
      });
    }
  },

  // 获取订单统计
  getOrderStats: function() {
    // 实际开发中应该调用API获取订单统计数据
    // 这里使用模拟数据
    this.setData({
      orderStats: {
        pendingPayment: 0,
        pendingDelivery: 0,
        pendingReceipt: 0,
        completed: 0
      }
    });
  },

  // 获取维修订单统计
  getRepairStats: function() {
    const openId = app.globalData.openId;

    if (!openId) {
      // 尝试从本地存储中获取openId
      tt.getStorage({
        key: 'openId',
        success: (res) => {
          if (res.data) {
            this.fetchRepairStats(res.data);
          }
        }
      });
      return;
    }

    this.fetchRepairStats(openId);
  },

  // 获取维修订单统计数据
  fetchRepairStats: function(openId) {
    const api = require('../../utils/api');
    api.getRepairOrderStats(openId).then(res => {
      if (res.success && res.stats) {
        this.setData({
          repairStats: res.stats
        });
      } else {
        console.error('获取维修订单统计失败:', res.message);
      }
    }).catch(err => {
      console.error('获取维修订单统计请求失败:', err);
    });
  },

  // 登录
  login: function () {
    // 直接获取用户信息，必须由用户点击手势触发
    tt.getUserProfile({
                desc: '用于完善会员资料', // 抖音小程序特有参数，用于告知用户获取信息的用途
                success: (userRes) => {
                  console.log('getUserProfile返回的用户信息:', userRes);
                  const userInfo = userRes.userInfo;

                  // 保存到全局数据
                  app.globalData.userInfo = userInfo;

                  // 更新页面数据
                  this.setData({
                    userInfo: userInfo
                  });

                  // 获取用户信息后，再调用login获取code
                  tt.login({
                    success: (res) => {
                      console.log('登录响应:', res);
                      if (res.code) {
                        console.log('Login successful, code:', res.code);
                        const api = require('../../utils/api');

                        // 方式1：完整登录，发送 code 和用户信息到后端
                        api.login(res.code, userInfo).then(loginRes => {
                          if (loginRes.success) {
                            console.log('后端返回的完整登录结果:', loginRes);
                            // 登录成功，保存用户信息
                            app.globalData.userInfo = loginRes.userInfo;
                            app.globalData.userId = loginRes.userId;
                            app.globalData.openId = loginRes.openId;
                            app.globalData.sessionKey = loginRes.sessionKey;
                            app.globalData.isLoggedIn = true;

                            console.log('登录成功，用户ID:', loginRes.userId);
                            console.log('后端返回的用户信息:', loginRes.userInfo);

                            // 存储用户信息到本地
                            tt.setStorage({
                              key: 'userInfo',
                              data: {
                                userInfo: loginRes.userInfo,
                                userId: loginRes.userId,
                                openId: loginRes.openId,
                                sessionKey: loginRes.sessionKey
                              }
                            });

                            // 单独存储openId，方便其他页面使用
                            tt.setStorage({
                              key: 'openId',
                              data: loginRes.openId
                            });

                            // 更新页面数据
                            this.setData({
                              userInfo: loginRes.userInfo
                            });

                            tt.showToast({
                              title: '登录成功',
                              icon: 'success'
                            });
                          } else {
                            console.error('登录失败:', loginRes.message);
                            tt.showToast({
                              title: '登录失败',
                              icon: 'none'
                            });
                          }
                        }).catch(err => {
                          console.error('登录请求失败:', err);
                          tt.showToast({
                            title: '登录失败，请重试',
                            icon: 'none'
                          });
                        });

                        // 方式2：简化登录，只发送code到后端换取openId
                        // 使用简化登录
                        /*
                        api.code2Session(res.code).then(sessionRes => {
                          if (sessionRes.success) {
                            // 登录成功，保存openId和sessionKey
                            app.globalData.openId = sessionRes.openId;
                            app.globalData.sessionKey = sessionRes.sessionKey;
                            app.globalData.isLoggedIn = true;

                            if (!sessionRes.isNewUser && sessionRes.userId) {
                              app.globalData.userId = sessionRes.userId;
                            }

                            console.log('获取openId成功:', sessionRes.openId);

                            // 存储信息到本地
                            tt.setStorage({
                              key: 'sessionInfo',
                              data: {
                                openId: sessionRes.openId,
                                sessionKey: sessionRes.sessionKey,
                                userId: sessionRes.userId
                              }
                            });

                            // 单独存储openId，方便其他页面使用
                            tt.setStorage({
                              key: 'openId',
                              data: sessionRes.openId
                            });

                            // 更新页面数据
                            this.setData({
                              userInfo: {
                                ...userInfo,
                                userId: sessionRes.userId
                              }
                            });

                            tt.showToast({
                              title: '登录成功',
                              icon: 'success'
                            });
                          } else {
                            console.error('获取openId失败:', sessionRes.message);
                            tt.showToast({
                              title: '登录失败',
                              icon: 'none'
                            });
                          }
                        }).catch(err => {
                          console.error('获取openId请求失败:', err);
                          tt.showToast({
                            title: '登录失败，请重试',
                            icon: 'none'
                          });
                        });
                        */
                      } else {
                        console.log('Login failed:', res.errMsg);
                        tt.showToast({
                          title: '登录失败',
                          icon: 'none'
                        });
                      }
                    },
                    fail: (err) => {
                      console.error('Login error:', err);
                      tt.showToast({
                        title: '登录失败',
                        icon: 'none'
                      });
                    }
                  });
                },
                fail: (err) => {
                  console.error('Get user info failed:', err);
                  tt.showToast({
                    title: '获取用户信息失败',
                    icon: 'none'
                  });
                }
              });

  },

  // 跳转到订单列表
  goToOrderList: function(e) {
    const status = e.currentTarget.dataset.status;

    tt.showToast({
      title: '订单列表功能开发中',
      icon: 'none'
    });

    // 实际开发中应该跳转到订单列表页面
    // tt.navigateTo({
    //   url: `/pages/order_list/order_list?status=${status}`
    // });
  },

  // 跳转到售后服务
  goToAfterSale: function() {
    tt.showToast({
      title: '售后服务功能开发中',
      icon: 'none'
    });
  },

  // 跳转到维修列表
  goToRepairList: function(e) {
    try {
      // 获取状态参数
      const status = e.currentTarget.dataset.status;
      console.log('跳转到维修列表，状态:', status);

      // 根据状态跳转到不同的维修列表页面
      if (status === 'pending') {
        console.log('准备跳转到待接单页面');
        tt.navigateTo({
          url: '/pages/repair_list_pending/repair_list_pending',
          success: function() {
            console.log('跳转到待接单页面成功');
          },
          fail: function(err) {
            console.error('跳转到待接单页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else if (status === 'accepted') {
        console.log('准备跳转到待上门页面');
        tt.navigateTo({
          url: '/pages/repair_list_accepted/repair_list_accepted',
          success: function() {
            console.log('跳转到待上门页面成功');
          },
          fail: function(err) {
            console.error('跳转到待上门页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else if (status === 'processing') {
        console.log('准备跳转到维修中页面');
        tt.navigateTo({
          url: '/pages/repair_list_processing/repair_list_processing',
          success: function() {
            console.log('跳转到维修中页面成功');
          },
          fail: function(err) {
            console.error('跳转到维修中页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else if (status === 'completed') {
        console.log('准备跳转到已完成页面');
        tt.navigateTo({
          url: '/pages/repair_list_completed/repair_list_completed',
          success: function() {
            console.log('跳转到已完成页面成功');
          },
          fail: function(err) {
            console.error('跳转到已完成页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else {
        // 如果没有状态参数或状态参数不匹配，跳转到通用维修列表页面
        console.log('准备跳转到通用维修列表页面');
        tt.navigateTo({
          url: '/pages/repair_list/repair_list',
          success: function() {
            console.log('跳转到通用维修列表页面成功');
          },
          fail: function(err) {
            console.error('跳转到通用维修列表页面失败:', err);
            tt.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    } catch (error) {
      console.error('跳转异常:', error);
      tt.showToast({
        title: '页面跳转异常',
        icon: 'none'
      });
    }
  },

  // 跳转到地址列表
  goToAddressList: function() {
    tt.navigateTo({
      url: '/pages/address_list/address_list'
    });
  },

  // 跳转到收藏列表
  goToFavorites: function() {
    tt.showToast({
      title: '收藏列表功能开发中',
      icon: 'none'
    });
  },

  // 跳转到评价列表
  goToReviews: function() {
    tt.showToast({
      title: '评价列表功能开发中',
      icon: 'none'
    });
  },

  // 跳转到客服中心
  goToCustomerService: function() {
    tt.showToast({
      title: '客服中心功能开发中',
      icon: 'none'
    });
  },

  // 跳转到设置页面
  goToSettings: function() {
    tt.showToast({
      title: '设置功能开发中',
      icon: 'none'
    });
  },

  // 跳转到帮助中心
  goToHelpCenter: function() {
    tt.showToast({
      title: '帮助中心功能开发中',
      icon: 'none'
    });
  },

  // 跳转到加入我们
  goToAboutUs: function() {
    tt.navigateTo({
      url: '/pages/join_us/join_us'
    });
  }
})
