.container {
  padding: 30rpx;
}

.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.success-icon {
  width: 160rpx;
  height: 160rpx;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32rpx;
  color: #1e88e5;
  font-size: 80rpx;
}

.success-title {
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.success-message {
  font-size: 28rpx;
  color: #757575;
  margin-bottom: 48rpx;
  text-align: center;
  padding: 0 40rpx;
}

.repair-info {
  width: 100%;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  font-size: 28rpx;
}

.info-label {
  color: #757575;
}

.info-value {
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 32rpx;
  margin-top: 48rpx;
}

.action-btn {
  width: 300rpx;
  padding: 24rpx 0;
  text-align: center;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.primary-btn {
  background-color: #1e88e5;
  color: white;
}

.secondary-btn {
  background-color: #f5f5f5;
  color: #333333;
}

.engineer-card {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background-color: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.engineer-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  object-fit: cover;
}

.engineer-info {
  flex: 1;
}

.engineer-name {
  font-weight: 600;
  margin-bottom: 8rpx;
}

.engineer-title {
  font-size: 24rpx;
  color: #757575;
  margin-bottom: 8rpx;
}

.engineer-rating {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.engineer-rating .iconfont {
  color: #ffc107;
  margin-right: 4rpx;
}

.engineer-contact {
  display: flex;
  gap: 24rpx;
}

.contact-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 32rpx;
}

.call-btn {
  background-color: #1e88e5;
}

.message-btn {
  background-color: #4caf50;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  margin: 48rpx 0 32rpx;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mt-3 {
  margin-top: 24rpx;
}
