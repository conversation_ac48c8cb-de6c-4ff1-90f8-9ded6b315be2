const app = getApp()

Page({
  data: {
    appointmentId: '',
    faultTypeName: '',
    appointmentTime: '',
    address: '',
    openId: '', // 用户openId
    orderStatus: 'pending', // 订单状态
    engineer: {
      name: '王工程师',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      title: '充电桩高级技师',
      experience: '8年维修经验',
      rating: 5,
      ratingValue: '5.0',
      reviewCount: 126,
      phone: '13812345678'
    },
    recommendProducts: [
      {
        id: 1,
        name: '快充Type-C充电枪',
        price: 299.00,
        image: '/images/products/快充充电枪.png'
      },
      {
        id: 2,
        name: '5米加长型充电线缆',
        price: 199.00,
        image: '/images/products/充电线缆.png'
      }
    ]
  },

  onLoad: function (options) {
    console.log('Repair success page loaded');

    // 获取预约ID
    const appointmentId = options.id || '';

    this.setData({
      appointmentId: appointmentId
    });

    // 获取openId
    this.getOpenId();
  },

  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
      this.getAppointmentInfo(this.data.appointmentId);
    } else {
      // 从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.openId) {
            this.setData({
              openId: res.data.openId
            });
            this.getAppointmentInfo(this.data.appointmentId);
          } else {
            // 尝试从openId存储中获取
            tt.getStorage({
              key: 'openId',
              success: (res) => {
                if (res.data) {
                  this.setData({
                    openId: res.data
                  });
                  this.getAppointmentInfo(this.data.appointmentId);
                } else {
                  this.showLoginTip();
                }
              },
              fail: () => {
                this.showLoginTip();
              }
            });
          }
        },
        fail: () => {
          // 尝试从openId存储中获取
          tt.getStorage({
            key: 'openId',
            success: (res) => {
              if (res.data) {
                this.setData({
                  openId: res.data
                });
                this.getAppointmentInfo(this.data.appointmentId);
              } else {
                this.showLoginTip();
              }
            },
            fail: () => {
              this.showLoginTip();
            }
          });
        }
      });
    }
  },
  
  // 显示登录提示
  showLoginTip: function() {
    // 如果没有openId，提示用户登录
    tt.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    // 跳转到用户中心页面
    tt.switchTab({
      url: '/pages/user_center/user_center'
    });
  },

  // 获取预约信息
  getAppointmentInfo: function(appointmentId) {
    if (!appointmentId || !this.data.openId) {
      return;
    }

    tt.showLoading({
      title: '加载中...'
    });

    // 调用API获取维修订单详情
    const api = require('../../utils/api');
    api.getRepairOrderDetail(appointmentId, this.data.openId).then(res => {
      tt.hideLoading();
      
      if (res.success && res.order) {
        const order = res.order;
        
        // 获取故障类型名称
        const faultType = order.faultType;
        const faultTypes = [
          { type: 'no_charging', name: '无法充电' },
          { type: 'slow_charging', name: '充电慢' },
          { type: 'error_code', name: '报错代码' },
          { type: 'port_damage', name: '接口损坏' },
          { type: 'not_starting', name: '无法启动' },
          { type: 'overheating', name: '过热' },
          { type: 'display_issue', name: '显示故障' },
          { type: 'other', name: '其他故障' }
        ];

        const faultTypeObj = faultTypes.find(item => item.type === faultType);
        const faultTypeName = faultTypeObj ? faultTypeObj.name : '未知故障';

        // 拼接预约时间
        const appointmentTime = order.appointmentTime;

        this.setData({
          faultTypeName: faultTypeName,
          appointmentTime: appointmentTime,
          address: order.fullAddress, // 使用fullAddress字段
          orderStatus: order.status // 设置订单状态
        });
      } else {
        console.error('获取维修订单详情失败:', res.message);
        
        // 从全局数据中获取最近的预约
        const lastAppointment = app.globalData.lastAppointment;

        if (lastAppointment && lastAppointment.id === appointmentId) {
          // 获取故障类型名称
          const faultType = lastAppointment.faultType;
          const faultTypes = [
            { type: 'no_charging', name: '无法充电' },
            { type: 'slow_charging', name: '充电慢' },
            { type: 'error_code', name: '报错代码' },
            { type: 'port_damage', name: '接口损坏' },
            { type: 'not_starting', name: '无法启动' },
            { type: 'overheating', name: '过热' },
            { type: 'display_issue', name: '显示故障' },
            { type: 'other', name: '其他故障' }
          ];

          const faultTypeObj = faultTypes.find(item => item.type === faultType);
          const faultTypeName = faultTypeObj ? faultTypeObj.name : '未知故障';

          // 使用fullAddress字段
          const address = lastAppointment.fullAddress || '';

          // 拼接预约时间
          const appointmentTime = lastAppointment.appointmentDate + ' ' + lastAppointment.appointmentTime;

          this.setData({
            faultTypeName: faultTypeName,
            appointmentTime: appointmentTime,
            address: address,
            orderStatus: lastAppointment.status || 'pending'
          });
        } else {
          // 如果没有找到预约，使用默认值
          this.setData({
            faultTypeName: '充电桩无法启动',
            appointmentTime: '2023-05-18 14:00-15:00',
            address: '上海市浦东新区张江高科技园区博云路2号',
            orderStatus: 'pending'
          });
        }
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('获取维修订单详情请求失败:', err);
      
      // 从全局数据中获取最近的预约
      const lastAppointment = app.globalData.lastAppointment;

      if (lastAppointment && lastAppointment.id === appointmentId) {
        // 获取故障类型名称
        const faultType = lastAppointment.faultType;
        const faultTypes = [
          { type: 'no_charging', name: '无法充电' },
          { type: 'slow_charging', name: '充电慢' },
          { type: 'error_code', name: '报错代码' },
          { type: 'port_damage', name: '接口损坏' },
          { type: 'not_starting', name: '无法启动' },
          { type: 'overheating', name: '过热' },
          { type: 'display_issue', name: '显示故障' },
          { type: 'other', name: '其他故障' }
        ];

        const faultTypeObj = faultTypes.find(item => item.type === faultType);
        const faultTypeName = faultTypeObj ? faultTypeObj.name : '未知故障';

        // 使用fullAddress字段
        const address = lastAppointment.fullAddress || '';

        // 拼接预约时间
        const appointmentTime = lastAppointment.appointmentDate + ' ' + lastAppointment.appointmentTime;

        this.setData({
          faultTypeName: faultTypeName,
          appointmentTime: appointmentTime,
          address: address,
          orderStatus: lastAppointment.status || 'pending'
        });
      } else {
        // 如果没有找到预约，使用默认值
        this.setData({
          faultTypeName: '充电桩无法启动',
          appointmentTime: '2023-05-18 14:00-15:00',
          address: '上海市浦东新区张江高科技园区博云路2号',
          orderStatus: 'pending'
        });
      }
    });
  },

  // 返回首页
  goToHome: function() {
    // 使用reLaunch方法跳转到首页，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/index/index',
      success: () => {
        console.log('跳转到首页成功');
      },
      fail: (err) => {
        console.error('跳转到首页失败:', err);
      }
    });
  },

  // 查看预约
  goToAppointment: function() {
    tt.showToast({
      title: '预约详情功能开发中',
      icon: 'none'
    });

    // 实际开发中应该跳转到预约详情页面
    // tt.navigateTo({
    //   url: `/pages/appointment_detail/appointment_detail?id=${this.data.appointmentId}`
    // });
  },

  // 拨打工程师电话
  callEngineer: function() {
    tt.makePhoneCall({
      phoneNumber: this.data.engineer.phone
    });
  },

  // 发送消息给工程师
  messageEngineer: function() {
    tt.showToast({
      title: '消息功能开发中',
      icon: 'none'
    });
  },

  // 跳转到商品详情页
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    tt.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  }
})
