<view class="container">
  <!-- 成功信息 -->
  <view class="success-container">
    <view class="success-icon">
      <image src="/images/icons/成功.png" mode="aspectFit" style="width: 100rpx; height: 100rpx;"></image>
    </view>
    <view class="success-title">预约成功</view>
    <view class="success-message">您的维修预约已成功提交，工程师将按照预约时间上门服务</view>
    
    <view class="repair-info">
      <view class="info-item">
        <view class="info-label">预约编号</view>
        <view class="info-value">{{appointmentId}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">故障类型</view>
        <view class="info-value">{{faultTypeName}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">预约时间</view>
        <view class="info-value">{{appointmentTime}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">上门地址</view>
        <view class="info-value">{{address}}</view>
      </view>
    </view>
    
    <view class="action-buttons">
      <view class="action-btn secondary-btn" bindtap="goToHome">返回首页</view>
      <view class="action-btn primary-btn" bindtap="goToAppointment">查看预约</view>
    </view>
  </view>
  
  <!-- 工程师信息 -->
  <view class="engineer-card">
    <image src="{{engineer.avatar}}" class="engineer-avatar" mode="aspectFill" />
    <view class="engineer-info">
      <view class="engineer-name">{{engineer.name}}</view>
      <view class="engineer-title">{{engineer.title}} | {{engineer.experience}}</view>
      <view class="engineer-rating">
        <text class="iconfont icon-star" tt:for="{{engineer.rating}}" tt:key="*this"></text>
        <text>{{engineer.ratingValue}} ({{engineer.reviewCount}}次评价)</text>
      </view>
    </view>
    <view class="engineer-contact">
      <view class="contact-btn call-btn" bindtap="callEngineer">
        <image src="/images/icons/电话.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
      </view>
      <view class="contact-btn message-btn" bindtap="messageEngineer">
        <image src="/images/icons/发消息.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;"></image>
      </view>
    </view>
  </view>
  
  <!-- 维修小贴士 -->
  <view class="card">
    <view class="text-sm font-bold mb-2">维修小贴士</view>
    <view class="text-sm">1. 请确保预约时间有人在家，以便工程师能够顺利上门服务</view>
    <view class="text-sm">2. 如需更改预约时间，请提前2小时联系客服或工程师</view>
    <view class="text-sm">3. 工程师上门前会提前联系您确认地址和时间</view>
    <view class="text-sm">4. 维修完成后，请对工程师的服务进行评价，您的反馈对我们很重要</view>
  </view>
  
  <!-- 相关配件推荐 -->
  <view class="section-title mt-3">
    <text>相关配件推荐</text>
  </view>
  
  <view class="grid">
    <view class="product-card" tt:for="{{recommendProducts}}" tt:key="id" bindtap="goToProductDetail" data-id="{{item.id}}">
      <image src="{{item.image}}" mode="aspectFill" />
      <view class="info">
        <view class="title">{{item.name}}</view>
        <view class="price">¥{{item.price}}</view>
      </view>
    </view>
  </view>
</view>
