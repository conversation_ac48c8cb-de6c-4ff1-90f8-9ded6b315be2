const app = getApp()

Page({
  data: {
    orders: [],
    openId: '',
    faultTypeMap: {
      'no_charging': '无法充电',
      'slow_charging': '充电慢',
      'error_code': '报错代码',
      'port_damage': '接口损坏',
      'not_starting': '无法启动',
      'overheating': '过热',
      'display_issue': '显示故障',
      'other': '其他故障'
    }
  },

  onLoad: function () {
    console.log('Repair list accepted page loaded');
    
    // 获取openId
    this.getOpenId();
  },

  onShow: function () {
    // 如果已经有openId，则获取订单列表
    if (this.data.openId) {
      this.getOrderList();
    }
  },

  // 获取openId
  getOpenId: function() {
    // 从全局数据中获取openId
    if (app.globalData.openId) {
      this.setData({
        openId: app.globalData.openId
      });
      this.getOrderList();
    } else {
      // 从本地存储中获取
      tt.getStorage({
        key: 'userInfo',
        success: (res) => {
          if (res.data && res.data.openId) {
            this.setData({
              openId: res.data.openId
            });
            this.getOrderList();
          } else {
            // 尝试从openId存储中获取
            tt.getStorage({
              key: 'openId',
              success: (res) => {
                if (res.data) {
                  this.setData({
                    openId: res.data
                  });
                  this.getOrderList();
                } else {
                  this.showLoginTip();
                }
              },
              fail: () => {
                this.showLoginTip();
              }
            });
          }
        },
        fail: () => {
          // 尝试从openId存储中获取
          tt.getStorage({
            key: 'openId',
            success: (res) => {
              if (res.data) {
                this.setData({
                  openId: res.data
                });
                this.getOrderList();
              } else {
                this.showLoginTip();
              }
            },
            fail: () => {
              this.showLoginTip();
            }
          });
        }
      });
    }
  },
  
  // 显示登录提示
  showLoginTip: function() {
    // 如果没有openId，提示用户登录
    tt.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    // 跳转到用户中心页面
    tt.switchTab({
      url: '/pages/user_center/user_center'
    });
  },

  // 获取订单列表
  getOrderList: function() {
    if (!this.data.openId) {
      console.error('获取订单列表失败：openId为空');
      return;
    }

    tt.showLoading({
      title: '加载中...'
    });

    const api = require('../../utils/api');
    api.getRepairOrderList(this.data.openId, 'accepted').then(res => {
      tt.hideLoading();
      
      if (res.success) {
        // 格式化创建时间
        const orders = res.orders.map(order => {
          if (order.createdAt) {
            const date = new Date(order.createdAt);
            order.createdAtFormatted = `${date.getFullYear()}-${this.padZero(date.getMonth() + 1)}-${this.padZero(date.getDate())} ${this.padZero(date.getHours())}:${this.padZero(date.getMinutes())}`;
          } else {
            order.createdAtFormatted = '未知';
          }
          return order;
        });

        this.setData({
          orders: orders
        });
      } else {
        console.error('获取订单列表失败:', res.message);
        tt.showToast({
          title: '获取订单列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      tt.hideLoading();
      console.error('获取订单列表请求失败:', err);
      tt.showToast({
        title: '获取订单列表失败',
        icon: 'none'
      });
    });
  },

  // 跳转到订单详情
  goToOrderDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    
    tt.navigateTo({
      url: `/pages/repair_detail/repair_detail?id=${id}`
    });
  },

  // 拨打工程师电话
  callEngineer: function(e) {
    const phone = e.currentTarget.dataset.phone;
    
    if (!phone) {
      tt.showToast({
        title: '电话号码不能为空',
        icon: 'none'
      });
      return;
    }

    tt.makePhoneCall({
      phoneNumber: phone
    });
  },

  // 联系客服
  contactService: function() {
    // 跳转到客服页面
    tt.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 去预约维修
  goToRepair: function() {
    tt.navigateTo({
      url: '/pages/repair_form/repair_form'
    });
  },

  // 数字补零
  padZero: function(num) {
    return num < 10 ? '0' + num : num;
  }
})
