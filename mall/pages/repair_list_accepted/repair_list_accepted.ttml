<view class="container">
  <view class="header">
    <view class="title">待上门</view>
    <view class="subtitle">工程师已接单，将按预约时间上门服务</view>
  </view>

  <view class="content">
    <block tt:if="{{orders.length > 0}}">
      <view class="order-list">
        <view class="order-item" tt:for="{{orders}}" tt:key="id" bindtap="goToOrderDetail" data-id="{{item.orderNo}}">
          <view class="order-header">
            <view class="order-no">订单号: {{item.orderNo}}</view>
            <view class="order-status">
              <text class="status-text status-accepted">待上门</text>
            </view>
          </view>
          <view class="order-info">
            <view class="info-item">
              <text class="label">故障类型:</text>
              <text class="value">{{faultTypeMap[item.faultType] || '未知故障'}}</text>
            </view>
            <view class="info-item">
              <text class="label">充电桩型号:</text>
              <text class="value">{{item.model}}</text>
            </view>
            <view class="info-item">
              <text class="label">预约时间:</text>
              <text class="value">{{item.appointmentTime}}</text>
            </view>
            <view class="info-item">
              <text class="label">服务地址:</text>
              <text class="value address">{{item.fullAddress}}</text>
            </view>
          </view>
          <view class="engineer-info" tt:if="{{item.engineerName}}">
            <view class="engineer">
              <view class="engineer-avatar">
                <image tt:if="{{item.engineerAvatar}}" class="avatar-img" src="{{item.engineerAvatar}}" mode="aspectFill"></image>
                <view tt:else class="avatar-placeholder">
                  <text class="avatar-text">{{item.engineerName.charAt(0)}}</text>
                </view>
              </view>
              <view class="engineer-detail">
                <view class="engineer-name">{{item.engineerName}}</view>
                <view class="engineer-title">充电桩维修工程师</view>
                <view class="engineer-phone" tt:if="{{item.engineerPhone}}">{{item.engineerPhone}}</view>
              </view>
              <view class="engineer-contact">
                <button class="contact-btn" catchtap="callEngineer" data-phone="{{item.engineerPhone}}">
                  <text class="contact-text">联系</text>
                </button>
              </view>
            </view>
          </view>
          <view class="order-footer">
            <view class="time">创建时间: {{item.createdAtFormatted}}</view>
            <view class="actions">
              <button class="btn btn-default" catchtap="contactService">联系客服</button>
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-state" tt:else>
      <image class="empty-icon" src="/images/icons/empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无待上门订单</text>
    </view>
  </view>
</view>
