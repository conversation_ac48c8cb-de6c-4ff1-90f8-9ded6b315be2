.container {
  padding: 30rpx 30rpx 0;
}

.section {
  margin-bottom: 40rpx;
}

.repair-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

/* 覆盖全局样式中的部分属性 */
.banner {
  height: 300rpx;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
}

.banner image {
  width: 100%;
  height: 100%;
}

.category-list {
  white-space: nowrap;
  margin-bottom: 40rpx;
}

.category-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-right: 32rpx;
  width: 120rpx;
}

.category-icon {
  width: 100rpx;
  height: 100rpx;
  background-color: #e3f2fd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  color: #1e88e5;
  font-size: 40rpx;
}