const app = getApp()

Page({
  data: {
    banners: [
      {
        id: 1,
        image: '/images/轮播图/轮播图1.png',
        type: 'repair'
      },
      {
        id: 2,
        image: '/images/轮播图/轮播图2.png',
        type: 'product'
      },
      {
        id: 3,
        image: 'https://images.unsplash.com/photo-1581092580497-e0d23cbdf1dc?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
        type: 'service'
      }
    ],
    categories: [
      {
        id: 1,
        name: '故障报修',
        image:'/images/icons/故障报修.png',
        type: 'repair'
      },
      {
        id: 2,
        name: '配件商城',
        image:'/images/icons/商城.png',
        type: 'mall'
      },
      {
        id: 3,
        name: '服务网点',
        image:'/images/icons/服务网点.png',
        type: 'service'
      },
      {
        id: 4,
        name: '在线咨询',
        image:'/images/icons/在线咨询.png',
        type: 'consult'
      },
      {
        id: 5,
        name: '行业资讯',
        image:'/images/icons/新闻审核单-新闻时长.png',
        type: 'news'
      }
    ],
    hotProducts: [
      {
        id: 1,
        name: '快充充电枪',
        price: 299.00,
        image: '/images/products/快充充电枪.png'
      },
      {
        id: 2,
        name: '5米加长型充电线缆',
        price: 199.00,
        image: '/images/products/充电线缆.png'
      }
    ],
    commonFaults: [
      {
        id: 1,
        name: '无法充电',
        image: '/images/icons/无法充电.png',
        type: 'no_charging'
      },
      {
        id: 2,
        name: '充电慢',
        image: '/images/icons/充电慢.png',
        type: 'slow_charging'
      },
      {
        id: 3,
        name: '报错代码',
        image: '/images/icons/错误代码.png',
        type: 'error_code'
      }
    ],
    reviews: [
      {
        id: 1,
        name: '张先生',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        rating: 5,
        content: '师傅很专业，上门维修速度快，解决了我的充电桩无法启动的问题，价格也很合理，非常满意！'
      },
      {
        id: 2,
        name: '李女士',
        avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        rating: 4,
        content: '在这里买的充电枪质量很好，客服态度也很好，有问题都能及时解答，会继续支持！'
      }
    ],
    news: [
      {
        id: 1,
        title: '新能源汽车充电桩行业发展趋势分析',
        summary: '随着新能源汽车的普及，充电桩行业迎来了快速发展期。本文分析了当前充电桩行业的发展趋势和未来前景...',
        date: '2023-05-15'
      },
      {
        id: 2,
        title: '如何正确使用和维护家用充电桩？',
        summary: '家用充电桩的正确使用和日常维护对延长设备寿命至关重要。本文介绍了家用充电桩的使用注意事项和维护方法...',
        date: '2023-05-10'
      }
    ]
  },

  onLoad: function () {
    // 页面加载时执行
    console.log('Index page loaded');
  },

  onShow: function () {
    // 页面显示时执行

    // 更新自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
      console.log('首页更新tabBar成功');
    } else {
      console.log('首页获取tabBar失败');
    }
  },

  // 搜索框点击
  goToSearch: function() {
    tt.showToast({
      title: '搜索功能开发中',
      icon: 'none'
    });
  },

  // Banner点击
  onBannerTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const banner = this.data.banners.find(item => item.id === id);

    if (banner.type === 'repair') {
      this.goToRepair();
    } else if (banner.type === 'product') {
      this.goToMall();
    } else if (banner.type === 'service') {
      this.goToServiceCenters();
    }
  },

  // 分类点击
  onCategoryTap: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log(type)

    switch(type) {
      case 'repair':
        this.goToRepair();
        break;
      case 'mall':
        this.goToMall();
        break;
      case 'service':
        this.goToServiceCenters();
        break;
      case 'consult':
        this.goToConsult();
        break;
      case 'news':
        this.goToNews();
        break;
    }
  },

  // 跳转到商城页面
  goToMall: function() {
    // 使用reLaunch方法跳转到商城页面，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/mall/mall',
      success: () => {
        console.log('跳转到商城页面成功');
      },
      fail: (err) => {
        console.error('跳转到商城页面失败:', err);
      }
    });
  },

  // 跳转到产品详情页
  goToProductDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    tt.navigateTo({
      url: `/pages/product_detail/product_detail?id=${id}`
    });
  },

  // 跳转到维修页面
  goToRepair: function() {
    // 使用reLaunch方法跳转到维修页面，并关闭所有其他页面
    tt.reLaunch({
      url: '/pages/repair/repair',
      success: () => {
        console.log('跳转到维修页面成功');
      },
      fail: (err) => {
        console.error('跳转到维修页面失败:', err);
      }
    });
  },

  // 跳转到维修表单页面
  goToRepairForm: function(e) {
    const type = e.currentTarget.dataset.type;
    tt.navigateTo({
      url: `/pages/repair_form/repair_form?type=${type}`
    });
  },

  // 跳转到服务网点页面
  goToServiceCenters: function() {
    tt.navigateTo({
      url: '/pages/service_centers/service_centers'
    });
  },

  // 跳转到在线咨询
  goToConsult: function() {
    tt.navigateTo({
      url: '/pages/customer_service/customer_service'
    });
  },

  // 跳转到新闻页面
  goToNews: function() {
    tt.showToast({
      title: '新闻页面开发中',
      icon: 'none'
    });
  },

  // 跳转到新闻详情
  goToNewsDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    tt.showToast({
      title: `新闻ID: ${id}`,
      icon: 'none'
    });
  }
})
