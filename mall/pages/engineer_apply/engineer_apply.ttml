<scroll-view class="container" scroll-y="true">
  <!-- 申请须知 -->
  <view class="notice">
    <view class="notice-title">
      <text class="iconfont icon-info-circle"></text>
      <text>申请须知</text>
    </view>
    <view class="notice-content">
      <text>• 请确保提供的信息真实有效</text>
      <text>• 资质证书必须清晰可见</text>
      <text>• 年龄需在18-65岁之间</text>
      <text>• 审核周期为3-5个工作日</text>
    </view>
  </view>

  <!-- 基本信息 -->
  <view class="form-section">
    <view class="section-title">基本信息</view>

    <view class="form-item">
      <view class="form-label">姓名 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入真实姓名"
        value="{{formData.name}}"
        data-field="name"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">联系电话 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入联系电话"
        type="number"
        value="{{formData.phone}}"
        data-field="phone"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">邮箱地址</view>
      <input
        class="form-input"
        placeholder="请输入邮箱地址"
        value="{{formData.email}}"
        data-field="email"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">性别 <text class="required">*</text></view>
      <picker
        class="form-picker"
        bindchange="onGenderChange"
        value="{{formData.gender}}"
        range="{{genderOptions}}"
        range-key="label"
      >
        <view class="picker-text {{formData.gender ? '' : 'placeholder'}}">
          {{formData.gender || '请选择性别'}}
        </view>
      </picker>
    </view>

    <view class="form-item">
      <view class="form-label">年龄 <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入年龄"
        type="number"
        value="{{formData.age}}"
        data-field="age"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">身份证号</view>
      <input
        class="form-input"
        placeholder="请输入身份证号（选填）"
        value="{{formData.idCard}}"
        data-field="idCard"
        bindinput="onInputChange"
      />
    </view>
  </view>

  <!-- 专业信息 -->
  <view class="form-section">
    <view class="section-title">专业信息</view>

    <view class="form-item">
      <view class="form-label">专业领域 <text class="required">*</text></view>
      <view class="checkbox-group">
        <view
          class="checkbox-item {{item.checked ? 'checked' : ''}}"
          tt:for="{{specialtyOptions}}"
          tt:key="id"
          bindtap="onSpecialtyChange"
          data-index="{{index}}"
        >
          <text class="checkbox-icon">{{item.checked ? '✓' : ''}}</text>
          <text class="checkbox-text">{{item.name}}</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-label">工作经验（年） <text class="required">*</text></view>
      <input
        class="form-input"
        placeholder="请输入工作经验年数"
        type="number"
        value="{{formData.experienceYears}}"
        data-field="experienceYears"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">学历</view>
      <picker
        class="form-picker"
        bindchange="onEducationChange"
        value="{{formData.education}}"
        range="{{educationOptions}}"
        range-key="label"
      >
        <view class="picker-text {{formData.education ? '' : 'placeholder'}}">
          {{formData.education || '请选择学历'}}
        </view>
      </picker>
    </view>

    <view class="form-item">
      <view class="form-label">技能标签</view>
      <view class="checkbox-group">
        <view
          class="checkbox-item {{item.checked ? 'checked' : ''}}"
          tt:for="{{skillOptions}}"
          tt:key="id"
          bindtap="onSkillChange"
          data-index="{{index}}"
        >
          <text class="checkbox-icon">{{item.checked ? '✓' : ''}}</text>
          <text class="checkbox-text">{{item.name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 工作信息 -->
  <view class="form-section">
    <view class="section-title">工作信息</view>

    <view class="form-item">
      <view class="form-label">服务区域 <text class="required">*</text></view>
      <view class="checkbox-group">
        <view
          class="checkbox-item {{item.checked ? 'checked' : ''}}"
          tt:for="{{workAreaOptions}}"
          tt:key="id"
          bindtap="onWorkAreaChange"
          data-index="{{index}}"
        >
          <text class="checkbox-icon">{{item.checked ? '✓' : ''}}</text>
          <text class="checkbox-text">{{item.name}}</text>
        </view>
      </view>
    </view>

    <view class="form-item">
      <view class="form-label">工作时间</view>
      <input
        class="form-input"
        placeholder="如：周一至周日 9:00-18:00"
        value="{{formData.workTime}}"
        data-field="workTime"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">期望时薪（元/小时）</view>
      <input
        class="form-input"
        placeholder="请输入期望时薪"
        type="digit"
        value="{{formData.hourlyRate}}"
        data-field="hourlyRate"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <view class="form-label">上门服务费（元）</view>
      <input
        class="form-input"
        placeholder="请输入上门服务费"
        type="digit"
        value="{{formData.serviceFee}}"
        data-field="serviceFee"
        bindinput="onInputChange"
      />
    </view>
  </view>

  <!-- 个人介绍 -->
  <view class="form-section">
    <view class="section-title">个人介绍</view>

    <view class="form-item">
      <view class="form-label">个人简介</view>
      <textarea
        class="form-textarea"
        placeholder="请简要介绍您的工作经验和专业特长"
        value="{{formData.bio}}"
        data-field="bio"
        bindinput="onInputChange"
        maxlength="200"
      ></textarea>
    </view>

    <view class="form-item">
      <view class="form-label">详细介绍</view>
      <textarea
        class="form-textarea"
        placeholder="请详细介绍您的工作经历、技能水平、服务理念等"
        value="{{formData.introduction}}"
        data-field="introduction"
        bindinput="onInputChange"
        maxlength="1000"
      ></textarea>
    </view>
  </view>

  <!-- 资质证明 -->
  <view class="form-section">
    <view class="section-title">资质证明</view>

    <view class="form-item">
      <view class="form-label">资质证书 <text class="required">*</text></view>
      <view class="upload-grid">
        <view
          class="upload-item"
          tt:for="{{formData.certifications}}"
          tt:key="*this"
        >
          <image src="{{item}}" class="uploaded-image" mode="aspectFill"></image>
          <view class="delete-btn" bindtap="deleteCertificate" data-index="{{index}}">
            <text class="iconfont icon-close"></text>
          </view>
        </view>
        <view
          class="upload-item upload-placeholder"
          bindtap="uploadCertificates"
          tt:if="{{formData.certifications.length < 9}}"
        >
          <text class="iconfont icon-plus"></text>
          <text>添加证书</text>
        </view>
      </view>
      <view class="upload-tip">请上传电工证、技能证书、培训证书等相关资质</view>
    </view>
  </view>

  <!-- 工作照片 -->
  <view class="form-section">
    <view class="section-title">工作照片</view>

    <view class="form-item">
      <view class="form-label">工作照片</view>
      <view class="upload-grid">
        <view
          class="upload-item"
          tt:for="{{formData.workPhotos}}"
          tt:key="*this"
        >
          <image src="{{item}}" class="uploaded-image" mode="aspectFill"></image>
          <view class="delete-btn" bindtap="deleteWorkPhoto" data-index="{{index}}">
            <text class="iconfont icon-close"></text>
          </view>
        </view>
        <view
          class="upload-item upload-placeholder"
          bindtap="uploadWorkPhotos"
          tt:if="{{formData.workPhotos.length < 9}}"
        >
          <text class="iconfont icon-plus"></text>
          <text>添加照片</text>
        </view>
      </view>
      <view class="upload-tip">建议上传工作现场照片，展示您的专业技能</view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button
      class="submit-btn {{submitting ? 'submitting' : ''}}"
      bindtap="submitApplication"
      disabled="{{submitting}}"
    >
      {{submitting ? '提交中...' : '提交申请'}}
    </button>
    <view class="submit-tip">
      提交后我们将在3-5个工作日内完成审核，请保持电话畅通
    </view>
  </view>
</scroll-view>
