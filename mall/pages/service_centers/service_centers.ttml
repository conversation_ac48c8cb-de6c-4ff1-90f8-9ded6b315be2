<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <text class="iconfont icon-search"></text>
    <input type="text" placeholder="搜索服务网点" bindinput="onSearchInput" />
  </view>

  <!-- 地图 -->
  <view class="map-container">
    <map id="map"
         longitude="{{longitude}}"
         latitude="{{latitude}}"
         scale="{{scale}}"
         markers="{{markers}}"
         show-location="{{true}}"
         bindmarkertap="onMarkerTap">
    </map>
  </view>

  <!-- 筛选 -->
  <view class="tabs">
    <view class="tab {{sortType === 'all' ? 'active' : ''}}" bindtap="onSortTap" data-type="all">全部</view>
    <view class="tab {{sortType === 'distance' ? 'active' : ''}}" bindtap="onSortTap" data-type="distance">距离最近</view>
    <view class="tab {{sortType === 'rating' ? 'active' : ''}}" bindtap="onSortTap" data-type="rating">评分最高</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" tt:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 服务网点列表 -->
  <view class="service-center" tt:for="{{serviceCenters}}" tt:key="id" bindtap="onServiceCenterTap" data-id="{{item.id}}" tt:if="{{!loading}}">
    <view class="service-center-name">{{item.name}}</view>
    <view class="service-center-info">
      <text class="iconfont icon-map-marker"></text>
      <text>{{item.address}}</text>
    </view>
    <view class="service-center-info">
      <text class="iconfont icon-phone"></text>
      <text>{{item.phone}}</text>
    </view>
    <view class="service-center-info">
      <text class="iconfont icon-clock"></text>
      <text>营业时间: {{item.businessHours}}</text>
    </view>
    <view class="service-center-info">
      <text class="iconfont icon-star"></text>
      <text>{{item.rating}} ({{item.reviewCount}}条评价)</text>
    </view>
    <view class="service-center-info">
      <text class="iconfont icon-map"></text>
      <text>距离您: {{item.distance}}公里</text>
    </view>
    <view class="service-center-actions">
      <view class="action-button call-button" catchtap="callServiceCenter" data-phone="{{item.phone}}">
        <text class="iconfont icon-phone"></text>
        <text>电话</text>
      </view>
      <view class="action-button navigate-button" catchtap="navigateToServiceCenter" data-latitude="{{item.latitude}}" data-longitude="{{item.longitude}}" data-name="{{item.name}}">
        <text class="iconfont icon-location-arrow"></text>
        <text>导航</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty" tt:if="{{!loading && serviceCenters.length === 0}}">
    <text>暂无服务网点</text>
  </view>

  <!-- 加载更多 -->
  <view class="loading-more" tt:if="{{hasMore && !loading}}">
    <text>正在加载更多...</text>
  </view>
  <view class="no-more" tt:elif="{{!loading && serviceCenters.length > 0}}">
    <text>没有更多服务网点了</text>
  </view>
</view>
