Component({
  data: {
    selected: 0,
    list: [
      {
        pagePath: "/pages/index/index",
        iconPath: "/images/tabbar/home.png",
        selectedIconPath: "/images/tabbar/home_selected.png",
        text: "首页"
      },
      {
        pagePath: "/pages/mall/mall",
        iconPath: "/images/tabbar/mall.png",
        selectedIconPath: "/images/tabbar/mall_selected.png",
        text: "商城"
      },
      {
        pagePath: "/pages/repair/repair",
        iconPath: "/images/tabbar/repair.png",
        selectedIconPath: "/images/tabbar/repair_selected.png",
        text: "维修"
      },
      {
        pagePath: "/pages/user_center/user_center",
        iconPath: "/images/tabbar/user.png",
        selectedIconPath: "/images/tabbar/user_selected.png",
        text: "我的"
      }
    ]
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      
      tt.switchTab({
        url
      });
      
      this.setData({
        selected: data.index
      });
    }
  }
})
