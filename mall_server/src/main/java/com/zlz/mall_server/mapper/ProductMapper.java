package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.Products;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductMapper extends BaseMapper<Products> {

    /**
     * 根据分类ID查询商品列表
     * @param categoryId 分类ID，0表示查询所有
     * @return 商品列表
     */
    @Select("<script>" +
            "SELECT * FROM products WHERE status = 1 " +
            "<if test='categoryId != null and categoryId != 0'>" +
            "AND category_id = #{categoryId} " +
            "</if>" +
            "ORDER BY sort_order DESC, sales DESC, created_at DESC" +
            "</script>")
    List<Products> findByCategoryId(@Param("categoryId") Integer categoryId);

    /**
     * 根据分类ID和排序方式查询商品列表
     * @param categoryId 分类ID
     * @param sortType 排序方式：default, sales, price
     * @param priceOrder 价格排序：asc, desc
     * @return 商品列表
     */
    @Select("<script>" +
            "SELECT * FROM products WHERE status = 1 " +
            "<if test='categoryId != null and categoryId != 0'>" +
            "AND category_id = #{categoryId} " +
            "</if>" +
            "ORDER BY " +
            "<choose>" +
            "<when test='sortType == \"sales\"'>sales DESC</when>" +
            "<when test='sortType == \"price\" and priceOrder == \"asc\"'>price ASC</when>" +
            "<when test='sortType == \"price\" and priceOrder == \"desc\"'>price DESC</when>" +
            "<otherwise>sort_order DESC, sales DESC, created_at DESC</otherwise>" +
            "</choose>" +
            "</script>")
    List<Products> findByCategoryIdWithSort(@Param("categoryId") Integer categoryId,
                                           @Param("sortType") String sortType,
                                           @Param("priceOrder") String priceOrder);
}
