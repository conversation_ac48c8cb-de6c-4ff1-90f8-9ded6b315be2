package com.zlz.mall_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zlz.mall_server.model.RepairOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface RepairOrderMapper extends BaseMapper<RepairOrder> {
    
    /**
     * 根据openId查询用户维修订单列表
     * @param openId 用户openId
     * @return 维修订单列表
     */
    @Select("SELECT * FROM repair_orders WHERE open_id = #{openId} ORDER BY created_at DESC")
    List<RepairOrder> findByOpenId(String openId);
    
    /**
     * 根据openId和状态查询用户维修订单列表
     * @param openId 用户openId
     * @param status 订单状态
     * @return 维修订单列表
     */
    @Select("SELECT * FROM repair_orders WHERE open_id = #{openId} AND status = #{status} ORDER BY created_at DESC")
    List<RepairOrder> findByOpenIdAndStatus(String openId, String status);
    
    /**
     * 根据订单编号查询维修订单
     * @param orderNo 订单编号
     * @return 维修订单
     */
    @Select("SELECT * FROM repair_orders WHERE order_no = #{orderNo}")
    RepairOrder findByOrderNo(String orderNo);
}
