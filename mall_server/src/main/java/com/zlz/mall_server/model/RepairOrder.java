package com.zlz.mall_server.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("repair_orders")
public class RepairOrder {
    private Long id;
    
    private String orderNo; // 订单编号
    
    private String openId; // 用户openId
    
    private String faultType; // 故障类型
    
    private String model; // 充电桩型号
    
    private String description; // 故障描述
    
    private String name; // 联系人姓名
    
    private String phone; // 联系电话
    
    private Long addressId; // 地址ID
    
    private String fullAddress; // 完整地址
    
    private String serviceType; // 服务方式（home-上门维修，remote-远程指导）
    
    private String images; // 故障照片（JSON数组）
    
    private LocalDate appointmentDate; // 预约日期
    
    private String appointmentTime; // 预约时间段
    
    private String status; // 订单状态（pending-待接单，accepted-待上门，processing-维修中，completed-已完成）
    
    private Long engineerId; // 工程师ID
    
    private String engineerName; // 工程师姓名
    
    private String engineerPhone; // 工程师电话
    
    private BigDecimal repairFee; // 维修费用
    
    private BigDecimal partsFee; // 配件费用
    
    private BigDecimal totalFee; // 总费用
    
    private String remark; // 备注
    
    private LocalDateTime createdAt; // 创建时间
    
    private LocalDateTime updatedAt; // 更新时间
}
