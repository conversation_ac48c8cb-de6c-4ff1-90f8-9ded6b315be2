package com.zlz.mall_server.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/test")
public class TestAdminController {
    
    /**
     * 测试页面
     */
    @GetMapping("/admin")
    @ResponseBody
    public Map<String, Object> testAdmin() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "管理后台测试页面");
        result.put("info", "如果看到这个页面，说明基本配置正确");
        return result;
    }
    
    /**
     * 测试登录页面
     */
    @GetMapping("/login")
    public String testLogin() {
        return "admin/login";
    }
}
