package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.AdminUser;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.model.RepairOrder;
import com.zlz.mall_server.service.EngineerService;
import com.zlz.mall_server.service.ServiceCenterService;
import com.zlz.mall_server.service.RepairOrderService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.math.BigDecimal;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/admin/api")
public class AdminApiController {

    @Autowired
    private EngineerService engineerService;

    @Autowired
    private ServiceCenterService serviceCenterService;

    @Autowired
    private RepairOrderService repairOrderService;

    /**
     * 审核工程师申请
     */
    @PutMapping("/engineers/{id}/review")
    public Map<String, Object> reviewEngineer(@PathVariable Long id,
                                            @RequestParam String action,
                                            @RequestParam(required = false) String notes,
                                            jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            // 更新审核状态
            if ("approve".equals(action)) {
                engineer.setStatus("approved");
                engineer.setIsAvailable(true);
            } else if ("reject".equals(action)) {
                engineer.setStatus("rejected");
                engineer.setIsAvailable(false);
            } else {
                result.put("success", false);
                result.put("message", "无效的操作");
                return result;
            }

            engineer.setReviewTime(LocalDateTime.now());
            engineer.setReviewerId(admin.getId());
            engineer.setReviewNotes(notes);
            engineer.setUpdatedAt(LocalDateTime.now());

            engineerService.updateById(engineer);

            result.put("success", true);
            result.put("message", "approve".equals(action) ? "审核通过" : "审核拒绝");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 审核服务网点申请
     */
    @PutMapping("/service-centers/{id}/review")
    public Map<String, Object> reviewServiceCenter(@PathVariable Long id,
                                                 @RequestParam String action,
                                                 @RequestParam(required = false) String notes,
                                                 jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            ServiceCenter serviceCenter = serviceCenterService.getById(id);
            if (serviceCenter == null) {
                result.put("success", false);
                result.put("message", "服务网点不存在");
                return result;
            }

            // 更新审核状态
            if ("approve".equals(action)) {
                serviceCenter.setStatus("approved");
                serviceCenter.setIsActive(true);
            } else if ("reject".equals(action)) {
                serviceCenter.setStatus("rejected");
                serviceCenter.setIsActive(false);
            } else {
                result.put("success", false);
                result.put("message", "无效的操作");
                return result;
            }

            serviceCenter.setReviewTime(LocalDateTime.now());
            serviceCenter.setReviewerId(admin.getId());
            serviceCenter.setReviewNotes(notes);
            serviceCenter.setUpdatedAt(LocalDateTime.now());

            serviceCenterService.updateById(serviceCenter);

            result.put("success", true);
            result.put("message", "approve".equals(action) ? "审核通过" : "审核拒绝");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 删除工程师
     */
    @DeleteMapping("/engineers/{id}")
    public Map<String, Object> deleteEngineer(@PathVariable Long id, jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            boolean deleted = engineerService.removeById(id);
            if (deleted) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 删除服务网点
     */
    @DeleteMapping("/service-centers/{id}")
    public Map<String, Object> deleteServiceCenter(@PathVariable Long id, jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            boolean deleted = serviceCenterService.removeById(id);
            if (deleted) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/orders/{id}/status")
    public Map<String, Object> updateOrderStatus(@PathVariable Long id,
                                                @RequestParam String status,
                                                @RequestParam(required = false) String remark,
                                                jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            // 验证状态转换的合法性
            if (!isValidStatusTransition(order.getStatus(), status)) {
                result.put("success", false);
                result.put("message", "无效的状态转换");
                return result;
            }

            // 更新订单状态
            order.setStatus(status);
            if (remark != null && !remark.trim().isEmpty()) {
                order.setRemark(remark);
            }
            order.setUpdatedAt(LocalDateTime.now());

            boolean updated = repairOrderService.updateById(order);

            if (updated) {
                result.put("success", true);
                result.put("message", "状态更新成功");
                result.put("newStatus", getStatusText(status));
            } else {
                result.put("success", false);
                result.put("message", "状态更新失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 分配工程师
     */
    @PutMapping("/orders/{id}/assign")
    public Map<String, Object> assignEngineer(@PathVariable Long id,
                                             @RequestParam Long engineerId,
                                             jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            Engineer engineer = engineerService.getById(engineerId);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            // 分配工程师并更新状态
            order.setEngineerId(engineerId);
            order.setEngineerName(engineer.getName());
            order.setEngineerPhone(engineer.getPhone());
            order.setStatus("accepted"); // 分配工程师后状态变为已接单
            order.setUpdatedAt(LocalDateTime.now());

            boolean updated = repairOrderService.updateById(order);

            if (updated) {
                result.put("success", true);
                result.put("message", "工程师分配成功");
                result.put("engineerName", engineer.getName());
            } else {
                result.put("success", false);
                result.put("message", "分配失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取可用工程师列表
     */
    @GetMapping("/engineers/available")
    public Map<String, Object> getAvailableEngineers(jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            // 查询已审核通过且可用的工程师
            QueryWrapper<Engineer> query = new QueryWrapper<>();
            query.eq("status", "approved")
                 .eq("is_available", true)
                 .orderByDesc("rating")
                 .orderByDesc("completed_orders");

            List<Engineer> engineers = engineerService.list(query);

            result.put("success", true);
            result.put("data", engineers);
            result.put("message", "获取工程师列表成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工程师列表失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 完成订单
     */
    @PutMapping("/orders/{id}/complete")
    public Map<String, Object> completeOrder(@PathVariable Long id,
                                            @RequestBody Map<String, Object> completeData,
                                            jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            // 更新订单信息
            order.setStatus("completed");
            order.setCompletedAt(LocalDateTime.now());

            // 维修详情
            order.setRepairResult(completeData.get("repairResult").toString());
            order.setRepairTime(new BigDecimal(completeData.get("repairTime").toString()));
            order.setRepairDescription(completeData.get("repairDescription").toString());

            // 费用明细
            order.setLaborFee(new BigDecimal(completeData.get("laborFee").toString()));
            order.setServiceFee(new BigDecimal(completeData.get("serviceFee").toString()));
            order.setMaterialsFee(new BigDecimal(completeData.get("materialsFee").toString()));
            order.setTotalFee(new BigDecimal(completeData.get("totalAmount").toString()));

            // 耗材明细（JSON格式存储）
            order.setMaterialsDetail(convertMaterialsToJson(completeData.get("materials")));

            // 备注和账单状态
            order.setRemark(completeData.get("remark").toString());
            order.setBillSent(true);
            order.setBillSentTime(LocalDateTime.now());
            order.setUpdatedAt(LocalDateTime.now());

            boolean updated = repairOrderService.updateById(order);

            if (updated) {
                // 这里可以发送账单给用户（短信、微信消息等）
                sendBillToUser(order, completeData);

                result.put("success", true);
                result.put("message", "订单完成成功，账单已发送给用户");
            } else {
                result.put("success", false);
                result.put("message", "订单完成失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 发送账单给用户
     */
    private void sendBillToUser(RepairOrder order, Map<String, Object> completeData) {
        try {
            // 这里可以实现发送账单的逻辑
            // 1. 生成账单PDF
            // 2. 发送微信消息
            // 3. 发送短信通知
            // 4. 保存账单记录

            System.out.println("=== 维修账单 ===");
            System.out.println("订单编号：" + order.getOrderNo());
            System.out.println("客户姓名：" + order.getName());
            System.out.println("联系电话：" + order.getPhone());
            System.out.println("维修地址：" + order.getFullAddress());
            System.out.println("故障类型：" + order.getFaultType());
            System.out.println("设备型号：" + order.getModel());
            System.out.println("工程师：" + order.getEngineerName());
            System.out.println("维修结果：" + completeData.get("repairResult"));
            System.out.println("维修耗时：" + completeData.get("repairTime") + "小时");
            System.out.println("维修详情：" + completeData.get("repairDescription"));
            System.out.println("人工费：¥" + completeData.get("laborFee"));
            System.out.println("服务费：¥" + completeData.get("serviceFee"));
            System.out.println("耗材费：¥" + completeData.get("materialsFee"));
            System.out.println("总费用：¥" + completeData.get("totalAmount"));
            System.out.println("================");

        } catch (Exception e) {
            System.err.println("发送账单失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/orders/{id}/detail")
    public Map<String, Object> getOrderDetail(@PathVariable Long id, jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            result.put("success", true);
            result.put("data", order);
            result.put("message", "获取订单详情成功");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取订单详情失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 将耗材列表转换为JSON字符串
     */
    private String convertMaterialsToJson(Object materials) {
        try {
            if (materials == null) {
                return "[]";
            }

            // 如果已经是字符串，直接返回
            if (materials instanceof String) {
                return (String) materials;
            }

            // 如果是List，转换为JSON
            if (materials instanceof List) {
                List<?> materialsList = (List<?>) materials;
                StringBuilder json = new StringBuilder("[");

                for (int i = 0; i < materialsList.size(); i++) {
                    if (i > 0) json.append(",");

                    Map<?, ?> material = (Map<?, ?>) materialsList.get(i);
                    json.append("{")
                        .append("\"name\":\"").append(material.get("name")).append("\",")
                        .append("\"specification\":\"").append(material.get("specification")).append("\",")
                        .append("\"quantity\":").append(material.get("quantity")).append(",")
                        .append("\"unitPrice\":").append(material.get("unitPrice")).append(",")
                        .append("\"subtotal\":").append(material.get("subtotal"))
                        .append("}");
                }

                json.append("]");
                return json.toString();
            }

            return "[]";
        } catch (Exception e) {
            System.err.println("转换耗材JSON失败：" + e.getMessage());
            return "[]";
        }
    }

    /**
     * 验证状态转换是否合法
     */
    private boolean isValidStatusTransition(String currentStatus, String newStatus) {
        // 定义合法的状态转换
        switch (currentStatus) {
            case "pending":
                return "accepted".equals(newStatus);
            case "accepted":
                return "processing".equals(newStatus);
            case "processing":
                return "completed".equals(newStatus);
            case "completed":
                return false; // 已完成的订单不能再改变状态
            default:
                return false;
        }
    }

    /**
     * 获取状态的中文描述
     */
    private String getStatusText(String status) {
        switch (status) {
            case "pending":
                return "待接单";
            case "accepted":
                return "待上门";
            case "processing":
                return "维修中";
            case "completed":
                return "已完成";
            default:
                return status;
        }
    }
}
