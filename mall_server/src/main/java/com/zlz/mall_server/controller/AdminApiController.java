package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.AdminUser;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.service.EngineerService;
import com.zlz.mall_server.service.ServiceCenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/admin/api")
public class AdminApiController {
    
    @Autowired
    private EngineerService engineerService;
    
    @Autowired
    private ServiceCenterService serviceCenterService;
    
    /**
     * 审核工程师申请
     */
    @PutMapping("/engineers/{id}/review")
    public Map<String, Object> reviewEngineer(@PathVariable Long id,
                                            @RequestParam String action,
                                            @RequestParam(required = false) String notes,
                                            HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }
            
            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }
            
            // 更新审核状态
            if ("approve".equals(action)) {
                engineer.setStatus("approved");
                engineer.setIsAvailable(true);
            } else if ("reject".equals(action)) {
                engineer.setStatus("rejected");
                engineer.setIsAvailable(false);
            } else {
                result.put("success", false);
                result.put("message", "无效的操作");
                return result;
            }
            
            engineer.setReviewTime(LocalDateTime.now());
            engineer.setReviewerId(admin.getId());
            engineer.setReviewNotes(notes);
            engineer.setUpdatedAt(LocalDateTime.now());
            
            engineerService.updateById(engineer);
            
            result.put("success", true);
            result.put("message", "approve".equals(action) ? "审核通过" : "审核拒绝");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 审核服务网点申请
     */
    @PutMapping("/service-centers/{id}/review")
    public Map<String, Object> reviewServiceCenter(@PathVariable Long id,
                                                 @RequestParam String action,
                                                 @RequestParam(required = false) String notes,
                                                 HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }
            
            ServiceCenter serviceCenter = serviceCenterService.getById(id);
            if (serviceCenter == null) {
                result.put("success", false);
                result.put("message", "服务网点不存在");
                return result;
            }
            
            // 更新审核状态
            if ("approve".equals(action)) {
                serviceCenter.setStatus("approved");
                serviceCenter.setIsActive(true);
            } else if ("reject".equals(action)) {
                serviceCenter.setStatus("rejected");
                serviceCenter.setIsActive(false);
            } else {
                result.put("success", false);
                result.put("message", "无效的操作");
                return result;
            }
            
            serviceCenter.setReviewTime(LocalDateTime.now());
            serviceCenter.setReviewerId(admin.getId());
            serviceCenter.setReviewNotes(notes);
            serviceCenter.setUpdatedAt(LocalDateTime.now());
            
            serviceCenterService.updateById(serviceCenter);
            
            result.put("success", true);
            result.put("message", "approve".equals(action) ? "审核通过" : "审核拒绝");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 删除工程师
     */
    @DeleteMapping("/engineers/{id}")
    public Map<String, Object> deleteEngineer(@PathVariable Long id, HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }
            
            boolean deleted = engineerService.removeById(id);
            if (deleted) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 删除服务网点
     */
    @DeleteMapping("/service-centers/{id}")
    public Map<String, Object> deleteServiceCenter(@PathVariable Long id, HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }
            
            boolean deleted = serviceCenterService.removeById(id);
            if (deleted) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        
        return result;
    }
}
