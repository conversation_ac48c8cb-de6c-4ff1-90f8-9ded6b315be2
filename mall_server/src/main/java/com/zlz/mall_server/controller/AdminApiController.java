package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.AdminUser;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.model.ServiceCenter;
import com.zlz.mall_server.model.RepairOrder;
import com.zlz.mall_server.service.EngineerService;
import com.zlz.mall_server.service.ServiceCenterService;
import com.zlz.mall_server.service.RepairOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/admin/api")
public class AdminApiController {

    @Autowired
    private EngineerService engineerService;

    @Autowired
    private ServiceCenterService serviceCenterService;

    @Autowired
    private RepairOrderService repairOrderService;

    /**
     * 审核工程师申请
     */
    @PutMapping("/engineers/{id}/review")
    public Map<String, Object> reviewEngineer(@PathVariable Long id,
                                            @RequestParam String action,
                                            @RequestParam(required = false) String notes,
                                            jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            // 更新审核状态
            if ("approve".equals(action)) {
                engineer.setStatus("approved");
                engineer.setIsAvailable(true);
            } else if ("reject".equals(action)) {
                engineer.setStatus("rejected");
                engineer.setIsAvailable(false);
            } else {
                result.put("success", false);
                result.put("message", "无效的操作");
                return result;
            }

            engineer.setReviewTime(LocalDateTime.now());
            engineer.setReviewerId(admin.getId());
            engineer.setReviewNotes(notes);
            engineer.setUpdatedAt(LocalDateTime.now());

            engineerService.updateById(engineer);

            result.put("success", true);
            result.put("message", "approve".equals(action) ? "审核通过" : "审核拒绝");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 审核服务网点申请
     */
    @PutMapping("/service-centers/{id}/review")
    public Map<String, Object> reviewServiceCenter(@PathVariable Long id,
                                                 @RequestParam String action,
                                                 @RequestParam(required = false) String notes,
                                                 jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            ServiceCenter serviceCenter = serviceCenterService.getById(id);
            if (serviceCenter == null) {
                result.put("success", false);
                result.put("message", "服务网点不存在");
                return result;
            }

            // 更新审核状态
            if ("approve".equals(action)) {
                serviceCenter.setStatus("approved");
                serviceCenter.setIsActive(true);
            } else if ("reject".equals(action)) {
                serviceCenter.setStatus("rejected");
                serviceCenter.setIsActive(false);
            } else {
                result.put("success", false);
                result.put("message", "无效的操作");
                return result;
            }

            serviceCenter.setReviewTime(LocalDateTime.now());
            serviceCenter.setReviewerId(admin.getId());
            serviceCenter.setReviewNotes(notes);
            serviceCenter.setUpdatedAt(LocalDateTime.now());

            serviceCenterService.updateById(serviceCenter);

            result.put("success", true);
            result.put("message", "approve".equals(action) ? "审核通过" : "审核拒绝");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 删除工程师
     */
    @DeleteMapping("/engineers/{id}")
    public Map<String, Object> deleteEngineer(@PathVariable Long id, jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            boolean deleted = engineerService.removeById(id);
            if (deleted) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 删除服务网点
     */
    @DeleteMapping("/service-centers/{id}")
    public Map<String, Object> deleteServiceCenter(@PathVariable Long id, jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            boolean deleted = serviceCenterService.removeById(id);
            if (deleted) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/orders/{id}/status")
    public Map<String, Object> updateOrderStatus(@PathVariable Long id,
                                                @RequestParam String status,
                                                @RequestParam(required = false) String remark,
                                                jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            // 验证状态转换的合法性
            if (!isValidStatusTransition(order.getStatus(), status)) {
                result.put("success", false);
                result.put("message", "无效的状态转换");
                return result;
            }

            // 更新订单状态
            order.setStatus(status);
            if (remark != null && !remark.trim().isEmpty()) {
                order.setRemark(remark);
            }
            order.setUpdatedAt(LocalDateTime.now());

            boolean updated = repairOrderService.updateById(order);

            if (updated) {
                result.put("success", true);
                result.put("message", "状态更新成功");
                result.put("newStatus", getStatusText(status));
            } else {
                result.put("success", false);
                result.put("message", "状态更新失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 分配工程师
     */
    @PutMapping("/orders/{id}/assign")
    public Map<String, Object> assignEngineer(@PathVariable Long id,
                                             @RequestParam Long engineerId,
                                             jakarta.servlet.http.HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            jakarta.servlet.http.HttpSession session = request.getSession(false);
            if (session == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            RepairOrder order = repairOrderService.getById(id);
            if (order == null) {
                result.put("success", false);
                result.put("message", "订单不存在");
                return result;
            }

            Engineer engineer = engineerService.getById(engineerId);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }

            // 分配工程师并更新状态
            order.setEngineerId(engineerId);
            order.setEngineerName(engineer.getName());
            order.setEngineerPhone(engineer.getPhone());
            order.setStatus("accepted"); // 分配工程师后状态变为已接单
            order.setUpdatedAt(LocalDateTime.now());

            boolean updated = repairOrderService.updateById(order);

            if (updated) {
                result.put("success", true);
                result.put("message", "工程师分配成功");
                result.put("engineerName", engineer.getName());
            } else {
                result.put("success", false);
                result.put("message", "分配失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 验证状态转换是否合法
     */
    private boolean isValidStatusTransition(String currentStatus, String newStatus) {
        // 定义合法的状态转换
        switch (currentStatus) {
            case "pending":
                return "accepted".equals(newStatus);
            case "accepted":
                return "processing".equals(newStatus);
            case "processing":
                return "completed".equals(newStatus);
            case "completed":
                return false; // 已完成的订单不能再改变状态
            default:
                return false;
        }
    }

    /**
     * 获取状态的中文描述
     */
    private String getStatusText(String status) {
        switch (status) {
            case "pending":
                return "待接单";
            case "accepted":
                return "待上门";
            case "processing":
                return "维修中";
            case "completed":
                return "已完成";
            default:
                return status;
        }
    }
}
