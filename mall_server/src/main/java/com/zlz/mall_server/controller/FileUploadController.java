package com.zlz.mall_server.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@RestController
@RequestMapping("/api/upload")
@CrossOrigin(origins = "*")
public class FileUploadController {
    
    // 上传文件存储路径，可以在application.properties中配置
    @Value("${file.upload.path:/uploads/}")
    private String uploadPath;
    
    // 允许的图片格式
    private static final Set<String> ALLOWED_EXTENSIONS = Set.of("jpg", "jpeg", "png", "gif", "bmp", "webp");
    
    // 最大文件大小 (5MB)
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;
    
    /**
     * 单个图片上传
     */
    @PostMapping("/image")
    public Map<String, Object> uploadImage(@RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证文件
            String validationError = validateFile(file);
            if (validationError != null) {
                result.put("success", false);
                result.put("message", validationError);
                return result;
            }
            
            // 保存文件
            String savedFileName = saveFile(file);
            
            result.put("success", true);
            result.put("data", Map.of(
                "fileName", savedFileName,
                "originalName", file.getOriginalFilename(),
                "size", file.getSize(),
                "url", "/uploads/" + savedFileName
            ));
            result.put("message", "图片上传成功");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "图片上传失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 多个图片上传
     */
    @PostMapping("/images")
    public Map<String, Object> uploadImages(@RequestParam("files") MultipartFile[] files) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> uploadedFiles = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        
        try {
            for (MultipartFile file : files) {
                try {
                    // 验证文件
                    String validationError = validateFile(file);
                    if (validationError != null) {
                        errors.add(file.getOriginalFilename() + ": " + validationError);
                        continue;
                    }
                    
                    // 保存文件
                    String savedFileName = saveFile(file);
                    
                    uploadedFiles.add(Map.of(
                        "fileName", savedFileName,
                        "originalName", file.getOriginalFilename(),
                        "size", file.getSize(),
                        "url", "/uploads/" + savedFileName
                    ));
                    
                } catch (Exception e) {
                    errors.add(file.getOriginalFilename() + ": " + e.getMessage());
                }
            }
            
            result.put("success", true);
            result.put("data", Map.of(
                "uploadedFiles", uploadedFiles,
                "successCount", uploadedFiles.size(),
                "errorCount", errors.size(),
                "errors", errors
            ));
            result.put("message", String.format("上传完成，成功%d个，失败%d个", uploadedFiles.size(), errors.size()));
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "批量上传失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 验证上传文件
     */
    private String validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            return "文件不能为空";
        }
        
        if (file.getSize() > MAX_FILE_SIZE) {
            return "文件大小不能超过5MB";
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            return "文件名不能为空";
        }
        
        String extension = getFileExtension(originalFilename).toLowerCase();
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            return "只支持图片格式：" + String.join(", ", ALLOWED_EXTENSIONS);
        }
        
        return null;
    }
    
    /**
     * 保存文件到服务器
     */
    private String saveFile(MultipartFile file) throws IOException {
        // 创建上传目录
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }
        
        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String randomStr = UUID.randomUUID().toString().substring(0, 8);
        String savedFileName = timestamp + "_" + randomStr + "." + extension;
        
        // 保存文件
        Path filePath = Paths.get(uploadPath, savedFileName);
        Files.copy(file.getInputStream(), filePath);
        
        return savedFileName;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }
    
    /**
     * 删除文件
     */
    @DeleteMapping("/file/{fileName}")
    public Map<String, Object> deleteFile(@PathVariable String fileName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Path filePath = Paths.get(uploadPath, fileName);
            boolean deleted = Files.deleteIfExists(filePath);
            
            if (deleted) {
                result.put("success", true);
                result.put("message", "文件删除成功");
            } else {
                result.put("success", false);
                result.put("message", "文件不存在");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "文件删除失败：" + e.getMessage());
        }
        
        return result;
    }
}
