package com.zlz.mall_server.controller;

import com.zlz.mall_server.model.ProductCategory;
import com.zlz.mall_server.model.ProductReview;
import com.zlz.mall_server.model.Products;
import com.zlz.mall_server.service.ProductCategoryService;
import com.zlz.mall_server.service.ProductReviewService;
import com.zlz.mall_server.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/products")
@CrossOrigin(origins = "*")
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private ProductCategoryService categoryService;
    
    @Autowired
    private ProductReviewService reviewService;
    
    /**
     * 获取商品分类列表
     */
    @GetMapping("/categories")
    public Map<String, Object> getCategories() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ProductCategory> categories = categoryService.getAllEnabledCategories();
            result.put("success", true);
            result.put("data", categories);
            result.put("message", "获取分类列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取分类列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取商品列表
     */
    @GetMapping("/list")
    public Map<String, Object> getProductList(
            @RequestParam(value = "categoryId", defaultValue = "0") Integer categoryId,
            @RequestParam(value = "sortType", defaultValue = "default") String sortType,
            @RequestParam(value = "priceOrder", defaultValue = "desc") String priceOrder) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            List<Products> products;
            if ("default".equals(sortType)) {
                products = productService.getProductsByCategoryId(categoryId);
            } else {
                products = productService.getProductsByCategoryIdWithSort(categoryId, sortType, priceOrder);
            }
            
            result.put("success", true);
            result.put("data", products);
            result.put("message", "获取商品列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取商品列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取商品详情
     */
    @GetMapping("/detail/{id}")
    public Map<String, Object> getProductDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            Products product = productService.getProductDetail(id);
            if (product == null) {
                result.put("success", false);
                result.put("message", "商品不存在");
                return result;
            }
            
            // 获取商品评价
            List<ProductReview> reviews = reviewService.getReviewsByProductId(id);
            Integer reviewCount = reviewService.getReviewCountByProductId(id);
            Double avgRating = reviewService.getAverageRatingByProductId(id);
            
            Map<String, Object> productData = new HashMap<>();
            productData.put("product", product);
            productData.put("reviews", reviews);
            productData.put("reviewCount", reviewCount);
            productData.put("avgRating", avgRating);
            
            result.put("success", true);
            result.put("data", productData);
            result.put("message", "获取商品详情成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取商品详情失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取商品评价列表
     */
    @GetMapping("/{id}/reviews")
    public Map<String, Object> getProductReviews(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ProductReview> reviews = reviewService.getReviewsByProductId(id);
            Integer reviewCount = reviewService.getReviewCountByProductId(id);
            Double avgRating = reviewService.getAverageRatingByProductId(id);
            
            Map<String, Object> reviewData = new HashMap<>();
            reviewData.put("reviews", reviews);
            reviewData.put("reviewCount", reviewCount);
            reviewData.put("avgRating", avgRating);
            
            result.put("success", true);
            result.put("data", reviewData);
            result.put("message", "获取评价列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取评价列表失败：" + e.getMessage());
        }
        return result;
    }
}
