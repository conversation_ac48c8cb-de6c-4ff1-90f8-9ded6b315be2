package com.zlz.mall_server.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zlz.mall_server.model.*;
import com.zlz.mall_server.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private AdminUserService adminUserService;

    @Autowired
    private EngineerService engineerService;

    @Autowired
    private ServiceCenterService serviceCenterService;

    /**
     * 管理后台首页
     */
    @GetMapping({"", "/", "/index"})
    public String index(HttpServletRequest request, Model model) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return "redirect:/admin/login";
        }

        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        // 获取统计数据
        Map<String, Object> stats = new HashMap<>();

        // 工程师统计
        QueryWrapper<Engineer> engineerQuery = new QueryWrapper<>();
        stats.put("totalEngineers", engineerService.count());
        engineerQuery.eq("status", "pending");
        stats.put("pendingEngineers", engineerService.count(engineerQuery));
        engineerQuery.clear();
        engineerQuery.eq("status", "approved");
        stats.put("approvedEngineers", engineerService.count(engineerQuery));

        // 服务网点统计
        QueryWrapper<ServiceCenter> centerQuery = new QueryWrapper<>();
        stats.put("totalServiceCenters", serviceCenterService.count());
        centerQuery.eq("status", "pending");
        stats.put("pendingServiceCenters", serviceCenterService.count(centerQuery));
        centerQuery.clear();
        centerQuery.eq("status", "approved");
        stats.put("approvedServiceCenters", serviceCenterService.count(centerQuery));

        model.addAttribute("admin", admin);
        model.addAttribute("stats", stats);

        return "admin/index";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            AdminUser admin = (AdminUser) session.getAttribute("admin");
            if (admin != null) {
                return "redirect:/admin/index";
            }
        }
        return "admin/login";
    }

    /**
     * 处理登录
     */
    @PostMapping("/login")
    @ResponseBody
    public Map<String, Object> login(@RequestParam String username,
                                   @RequestParam String password,
                                   HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String ip = getClientIpAddress(request);
            AdminUser admin = adminUserService.login(username, password, ip);

            if (admin != null) {
                HttpSession session = request.getSession();
                session.setAttribute("admin", admin);
                result.put("success", true);
                result.put("message", "登录成功");
            } else {
                result.put("success", false);
                result.put("message", "用户名或密码错误");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 退出登录
     */
    @GetMapping("/logout")
    public String logout(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.removeAttribute("admin");
            session.invalidate();
        }
        return "redirect:/admin/login";
    }

    /**
     * 工程师管理页面
     */
    @GetMapping("/engineers")
    public String engineers(HttpServletRequest request, Model model,
                          @RequestParam(defaultValue = "1") int page,
                          @RequestParam(defaultValue = "10") int size,
                          @RequestParam(defaultValue = "") String status) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return "redirect:/admin/login";
        }

        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        // 分页查询工程师
        Page<Engineer> pageObj = new Page<>(page, size);
        QueryWrapper<Engineer> query = new QueryWrapper<>();
        if (!status.isEmpty()) {
            query.eq("status", status);
        }
        query.orderByDesc("created_at");

        IPage<Engineer> engineerPage = engineerService.page(pageObj, query);

        model.addAttribute("admin", admin);
        model.addAttribute("engineerPage", engineerPage);
        model.addAttribute("currentStatus", status);

        return "admin/engineers";
    }

    /**
     * 服务网点管理页面
     */
    @GetMapping("/service-centers")
    public String serviceCenters(HttpServletRequest request, Model model,
                                @RequestParam(defaultValue = "1") int page,
                                @RequestParam(defaultValue = "10") int size,
                                @RequestParam(defaultValue = "") String status) {
        HttpSession session = request.getSession(false);
        if (session == null) {
            return "redirect:/admin/login";
        }

        AdminUser admin = (AdminUser) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        // 分页查询服务网点
        Page<ServiceCenter> pageObj = new Page<>(page, size);
        QueryWrapper<ServiceCenter> query = new QueryWrapper<>();
        if (!status.isEmpty()) {
            query.eq("status", status);
        }
        query.orderByDesc("created_at");

        IPage<ServiceCenter> centerPage = serviceCenterService.page(pageObj, query);

        model.addAttribute("admin", admin);
        model.addAttribute("centerPage", centerPage);
        model.addAttribute("currentStatus", status);

        return "admin/service-centers";
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
