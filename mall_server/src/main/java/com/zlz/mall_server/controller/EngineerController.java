package com.zlz.mall_server.controller;

import com.zlz.mall_server.mapper.EngineerMapper.EngineerStats;
import com.zlz.mall_server.model.Engineer;
import com.zlz.mall_server.service.EngineerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/engineers")
@CrossOrigin(origins = "*")
public class EngineerController {
    
    @Autowired
    private EngineerService engineerService;
    
    /**
     * 获取所有已审核通过的工程师列表
     */
    @GetMapping("/approved")
    public Map<String, Object> getApprovedEngineers() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Engineer> engineers = engineerService.getApprovedEngineers();
            
            // 处理JSON字段
            engineers.forEach(engineer -> {
                processEngineer<PERSON><PERSON><PERSON><PERSON>s(engineer);
            });
            
            result.put("success", true);
            result.put("data", engineers);
            result.put("message", "获取工程师列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工程师列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取在线且可接单的工程师列表
     */
    @GetMapping("/available")
    public Map<String, Object> getAvailableEngineers() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Engineer> engineers = engineerService.getAvailableEngineers();
            
            // 处理JSON字段
            engineers.forEach(engineer -> {
                processEngineerJsonFields(engineer);
            });
            
            result.put("success", true);
            result.put("data", engineers);
            result.put("message", "获取可接单工程师列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取可接单工程师列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据专业领域查询工程师
     */
    @GetMapping("/specialty/{specialty}")
    public Map<String, Object> getEngineersBySpecialty(@PathVariable String specialty) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Engineer> engineers = engineerService.getEngineersBySpecialty(specialty);
            
            // 处理JSON字段
            engineers.forEach(engineer -> {
                processEngineerJsonFields(engineer);
            });
            
            result.put("success", true);
            result.put("data", engineers);
            result.put("message", "获取专业工程师列表成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取专业工程师列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取工程师统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getEngineerStats() {
        Map<String, Object> result = new HashMap<>();
        try {
            EngineerStats stats = engineerService.getEngineerStats();
            
            result.put("success", true);
            result.put("data", stats);
            result.put("message", "获取工程师统计信息成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工程师统计信息失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取工程师详情
     */
    @GetMapping("/detail/{id}")
    public Map<String, Object> getEngineerDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            Engineer engineer = engineerService.getById(id);
            if (engineer == null) {
                result.put("success", false);
                result.put("message", "工程师不存在");
                return result;
            }
            
            // 处理JSON字段
            processEngineerJsonFields(engineer);
            
            result.put("success", true);
            result.put("data", engineer);
            result.put("message", "获取工程师详情成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取工程师详情失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 处理工程师的JSON字段
     * 将JSON字符串转换为前端可用的格式
     */
    private void processEngineerJsonFields(Engineer engineer) {
        // 这里可以根据需要处理JSON字段
        // 暂时保持原样，前端自行解析JSON字符串
    }
}
