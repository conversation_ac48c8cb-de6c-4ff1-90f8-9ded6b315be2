package com.zlz.mall_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zlz.mall_server.mapper.ProductMapper;
import com.zlz.mall_server.model.Products;
import com.zlz.mall_server.service.ProductService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Products> implements ProductService {
    
    @Override
    public List<Products> getProductsByCategoryId(Integer categoryId) {
        return baseMapper.findByCategoryId(categoryId);
    }
    
    @Override
    public List<Products> getProductsByCategoryIdWithSort(Integer categoryId, String sortType, String priceOrder) {
        return baseMapper.findByCategoryIdWithSort(categoryId, sortType, priceOrder);
    }
    
    @Override
    public Products getProductDetail(Long id) {
        return baseMapper.selectById(id);
    }
}
