-- 初始化所有数据库表和数据

-- 1. 创建商品分类表
CREATE TABLE IF NOT EXISTS `product_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `selected_icon` varchar(255) DEFAULT NULL COMMENT '选中状态图标',
  `parent_id` int(11) DEFAULT '0' COMMENT '父分类ID，0表示顶级分类',
  `level` tinyint(4) DEFAULT '1' COMMENT '分类层级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- 2. 创建商品表
CREATE TABLE IF NOT EXISTS `products` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(255) NOT NULL COMMENT '商品名称',
  `short_name` varchar(100) DEFAULT NULL COMMENT '商品简称（用于列表显示）',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价（用于显示折扣）',
  `sales` int(11) DEFAULT '0' COMMENT '销量',
  `stock` int(11) DEFAULT '0' COMMENT '库存',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand` varchar(100) DEFAULT NULL COMMENT '品牌',
  `model` varchar(100) DEFAULT NULL COMMENT '型号',
  `main_image` varchar(500) NOT NULL COMMENT '主图URL',
  `images` text COMMENT '商品图片组（JSON数组）',
  `detail_image` varchar(500) DEFAULT NULL COMMENT '详情图URL',
  `features` text COMMENT '产品特点（JSON数组）',
  `compatible_cars` text COMMENT '适用车型',
  `description` text COMMENT '商品描述',
  `specifications` text COMMENT '规格参数（JSON数组）',
  `specs` text COMMENT '规格选项（JSON数组）',
  `services` text COMMENT '服务说明（JSON数组）',
  `weight` decimal(8,2) DEFAULT NULL COMMENT '重量（kg）',
  `dimensions` varchar(100) DEFAULT NULL COMMENT '尺寸',
  `warranty_period` varchar(50) DEFAULT NULL COMMENT '保修期',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-下架 1-上架',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序权重',
  `seo_title` varchar(255) DEFAULT NULL COMMENT 'SEO标题',
  `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
  `seo_description` text COMMENT 'SEO描述',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sales` (`sales`),
  KEY `idx_price` (`price`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 3. 创建商品评价表
CREATE TABLE IF NOT EXISTS `product_reviews` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID（关联购买订单）',
  `order_item_id` bigint(20) DEFAULT NULL COMMENT '订单商品项ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户openId',
  `user_nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称（冗余字段，避免关联查询）',
  `user_avatar` varchar(255) DEFAULT NULL COMMENT '用户头像（冗余字段）',
  `rating` tinyint(4) NOT NULL COMMENT '评分 1-5星',
  `content` text NOT NULL COMMENT '评价内容',
  `images` text COMMENT '评价图片（JSON数组）',
  `spec_info` varchar(255) DEFAULT NULL COMMENT '购买规格信息',
  `is_anonymous` tinyint(1) DEFAULT '0' COMMENT '是否匿名评价 0-否 1-是',
  `reply_content` text COMMENT '商家回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '商家回复时间',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶 0-否 1-是',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 0-隐藏 1-显示 2-待审核',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品评价表';

-- 插入默认分类数据
INSERT INTO `product_categories` (`id`, `name`, `icon`, `selected_icon`, `parent_id`, `level`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
(1, '充电枪', '/images/icons/充电枪.png', '/images/icons/充电枪_selected.png', 0, 1, 1, 1, NOW(), NOW()),
(2, '线缆', '/images/icons/线缆.png', '/images/icons/线缆_selected.png', 0, 1, 2, 1, NOW(), NOW()),
(3, '模块', '/images/icons/模块.png', '/images/icons/模块_selected.png', 0, 1, 3, 1, NOW(), NOW()),
(4, '保护器', '/images/icons/保护器.png', '/images/icons/保护器_selected.png', 0, 1, 4, 1, NOW(), NOW()),
(5, '配件', '/images/icons/配件.png', '/images/icons/配件_selected.png', 0, 1, 5, 1, NOW(), NOW()),
(6, '工具', '/images/icons/工具.png', '/images/icons/工具_selected.png', 0, 1, 6, 1, NOW(), NOW());

-- 插入测试商品数据
INSERT INTO `products` (`id`, `name`, `short_name`, `price`, `original_price`, `sales`, `stock`, `category_id`, `brand`, `model`, `main_image`, `images`, `detail_image`, `features`, `compatible_cars`, `description`, `specifications`, `specs`, `services`, `weight`, `dimensions`, `warranty_period`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, '快充Type-C充电枪 60kW大功率 兼容多种车型', '快充Type-C充电枪', 299.00, 399.00, 128, 999, 1, '充电宝', 'TC-60K', '/images/products/快充充电枪.png',
'["/images/products/快充充电枪.png", "/images/products/快充枪2.png"]',
'/images/products/快充充电枪.png',
'["大功率快充：支持60kW大功率充电，充电速度提升50%", "兼容性强：适配市面上95%以上的电动汽车型号", "安全保障：多重保护机制，防过充、防过热、防短路", "耐用设计：采用高强度材料，使用寿命长达5年以上", "智能识别：自动识别车辆充电需求，优化充电效率"]',
'特斯拉、比亚迪、蔚来、小鹏、理想、宝马、奔驰等主流电动汽车品牌',
'高品质快充充电枪，适用于各种电动汽车',
'[{"name": "型号", "value": "TC-60K"}, {"name": "输入电压", "value": "AC 220V"}, {"name": "输出功率", "value": "60kW"}, {"name": "充电接口", "value": "Type-C"}, {"name": "线缆长度", "value": "3米"}, {"name": "工作温度", "value": "-20℃~60℃"}, {"name": "防护等级", "value": "IP65"}, {"name": "产品尺寸", "value": "120×80×50mm"}, {"name": "产品重量", "value": "1.2kg"}]',
'[{"id": 1, "name": "标准版"}, {"id": 2, "name": "豪华版"}, {"id": 3, "name": "专业版"}]',
'["包邮", "7天无理由退换", "质保1年"]',
1.20, '120×80×50mm', '1年', 1, 100, NOW(), NOW()),

(2, '5米加长型充电线缆', '5米充电线缆', 199.00, 249.00, 85, 500, 2, '线缆王', 'CL-5M', '/images/products/充电线缆.png',
'["/images/products/充电线缆.png"]',
'/images/products/充电线缆.png',
'["加长设计：5米长度，满足各种充电场景需求", "高品质材料：采用优质铜芯，传输效率高", "耐磨耐用：外层采用PVC材质，防水防晒", "安全保障：多重保护，防过热、防短路", "兼容性强：适配市面上主流充电桩和电动车型"]',
'适用于所有标准充电接口的电动汽车',
'高品质充电线缆，耐用可靠',
'[{"name": "型号", "value": "CL-5M"}, {"name": "长度", "value": "5米"}, {"name": "材质", "value": "铜芯+PVC外层"}, {"name": "最大电流", "value": "32A"}, {"name": "工作温度", "value": "-30℃~80℃"}, {"name": "防护等级", "value": "IP67"}, {"name": "产品重量", "value": "2.5kg"}]',
'[{"id": 1, "name": "3米"}, {"id": 2, "name": "5米"}, {"id": 3, "name": "8米"}]',
'["包邮", "7天无理由退换", "质保2年"]',
2.50, '5米长', '2年', 1, 90, NOW(), NOW()),

(3, '充电桩控制模块', '控制模块', 499.00, 599.00, 56, 200, 3, '智控科技', 'CM-Pro', '/images/products/充电控制模块.png',
'["/images/products/充电控制模块.png"]',
'/images/products/充电控制模块.png',
'["智能控制：支持远程监控和控制", "高效稳定：采用先进的控制算法", "安全可靠：多重安全保护机制", "易于安装：标准化接口设计", "兼容性强：支持多种充电桩型号"]',
'适用于各种品牌充电桩',
'专业充电桩控制模块，稳定可靠',
'[{"name": "型号", "value": "CM-Pro"}, {"name": "输入电压", "value": "AC 380V"}, {"name": "控制精度", "value": "±1%"}, {"name": "通信接口", "value": "RS485/CAN"}, {"name": "工作温度", "value": "-40℃~85℃"}, {"name": "防护等级", "value": "IP54"}]',
'[{"id": 1, "name": "基础版"}, {"id": 2, "name": "专业版"}, {"id": 3, "name": "旗舰版"}]',
'["包邮", "30天无理由退换", "质保3年"]',
0.80, '150×100×60mm', '3年', 1, 80, NOW(), NOW());

-- 插入测试评价数据
INSERT INTO `product_reviews` (`id`, `product_id`, `order_id`, `order_item_id`, `open_id`, `user_nickname`, `user_avatar`, `rating`, `content`, `images`, `spec_info`, `is_anonymous`, `reply_content`, `reply_time`, `is_top`, `like_count`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, NULL, NULL, 'test_user_1', '张先生', 'https://randomuser.me/api/portraits/men/32.jpg', 5, '质量非常好，充电速度快，兼容性也很强，推荐购买！', '["/images/products/快充充电枪.png"]', '标准版', 0, NULL, NULL, 0, 5, 1, '2023-05-10 10:30:00', '2023-05-10 10:30:00'),
(2, 1, NULL, NULL, 'test_user_2', '李女士', 'https://randomuser.me/api/portraits/women/44.jpg', 4, '充电速度确实快，但是价格稍微有点贵，总体还是很满意的。', '[]', '豪华版', 0, NULL, NULL, 0, 3, 1, '2023-05-05 14:20:00', '2023-05-05 14:20:00'),
(3, 2, NULL, NULL, 'test_user_3', '王先生', 'https://randomuser.me/api/portraits/men/22.jpg', 5, '线缆质量很好，长度刚好满足我的需求，充电稳定，没有发热现象。', '[]', '5米', 0, NULL, NULL, 0, 2, 1, '2023-05-12 09:15:00', '2023-05-12 09:15:00'),
(4, 2, NULL, NULL, 'test_user_4', '赵女士', 'https://randomuser.me/api/portraits/women/24.jpg', 4, '线缆不错，但是有点重，携带不太方便，其他都很满意。', '[]', '5米', 0, NULL, NULL, 0, 1, 1, '2023-05-08 16:45:00', '2023-05-08 16:45:00');

-- 执行工程师相关表的创建和数据插入
source mall_server/src/main/resources/db/engineers.sql;
