<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 充电桩服务平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin: 0.25rem 1rem;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .user-info {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            color: white;
            font-size: 1.2rem;
        }
        
        .order-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .order-card:hover {
            transform: translateY(-2px);
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-accepted {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .status-processing {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .action-btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-size: 0.85rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-assign {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-next {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        
        .btn-complete {
            background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
            color: #333;
        }
        
        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .filter-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: none;
            background: transparent;
            color: #666;
            margin-right: 1rem;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .order-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 0.25rem;
        }
        
        .info-value {
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <h6 class="text-white mb-1" th:text="${admin.realName}">管理员</h6>
                        <small class="text-white-50" th:text="${admin.role}">super_admin</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/index">
                                <i class="bi bi-house-door-fill me-2"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/engineers">
                                <i class="bi bi-people-fill me-2"></i>
                                工程师管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/service-centers">
                                <i class="bi bi-building me-2"></i>
                                服务网点管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/admin/orders">
                                <i class="bi bi-clipboard-check me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/logout">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-clipboard-check-fill text-primary me-2"></i>
                        订单管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 状态筛选 -->
                <div class="filter-tabs">
                    <div class="d-flex flex-wrap">
                        <button class="filter-tab" th:classappend="${currentStatus == '' ? 'active' : ''}" 
                                onclick="filterOrders('')">
                            <i class="bi bi-list-ul me-1"></i>全部订单
                        </button>
                        <button class="filter-tab" th:classappend="${currentStatus == 'pending' ? 'active' : ''}" 
                                onclick="filterOrders('pending')">
                            <i class="bi bi-hourglass-split me-1"></i>待接单
                        </button>
                        <button class="filter-tab" th:classappend="${currentStatus == 'accepted' ? 'active' : ''}" 
                                onclick="filterOrders('accepted')">
                            <i class="bi bi-person-check me-1"></i>待上门
                        </button>
                        <button class="filter-tab" th:classappend="${currentStatus == 'processing' ? 'active' : ''}" 
                                onclick="filterOrders('processing')">
                            <i class="bi bi-tools me-1"></i>维修中
                        </button>
                        <button class="filter-tab" th:classappend="${currentStatus == 'completed' ? 'active' : ''}" 
                                onclick="filterOrders('completed')">
                            <i class="bi bi-check-circle me-1"></i>已完成
                        </button>
                    </div>
                </div>

                <!-- 订单列表 -->
                <div class="row">
                    <div class="col-12" th:if="${orderPage.records.size() == 0}">
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">暂无订单数据</h4>
                            <p class="text-muted">当前筛选条件下没有找到订单</p>
                        </div>
                    </div>
                    
                    <div class="col-12" th:each="order : ${orderPage.records}">
                        <div class="order-card">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1" th:text="${order.orderNo}">订单编号</h5>
                                    <small class="text-muted" th:text="${#temporals.format(order.createdAt, 'yyyy-MM-dd HH:mm')}">创建时间</small>
                                </div>
                                <span class="status-badge" th:classappend="'status-' + ${order.status}" 
                                      th:text="${order.status == 'pending' ? '待接单' : 
                                               (order.status == 'accepted' ? '待上门' : 
                                               (order.status == 'processing' ? '维修中' : '已完成'))}">
                                    状态
                                </span>
                            </div>
                            
                            <div class="order-info">
                                <div class="info-item">
                                    <span class="info-label">故障类型</span>
                                    <span class="info-value" th:text="${order.faultType}">故障类型</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">设备型号</span>
                                    <span class="info-value" th:text="${order.model}">设备型号</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">联系人</span>
                                    <span class="info-value" th:text="${order.name}">联系人</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">联系电话</span>
                                    <span class="info-value" th:text="${order.phone}">联系电话</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">预约时间</span>
                                    <span class="info-value" th:text="${order.appointmentDate + ' ' + order.appointmentTime}">预约时间</span>
                                </div>
                                <div class="info-item" th:if="${order.engineerName}">
                                    <span class="info-label">分配工程师</span>
                                    <span class="info-value" th:text="${order.engineerName}">工程师</span>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <span class="info-label">服务地址</span>
                                <p class="info-value mb-0" th:text="${order.fullAddress}">服务地址</p>
                            </div>
                            
                            <div class="mb-3">
                                <span class="info-label">故障描述</span>
                                <p class="info-value mb-0" th:text="${order.description}">故障描述</p>
                            </div>
                            
                            <!-- 操作按钮 -->
                            <div class="d-flex flex-wrap">
                                <!-- 待接单状态：可以分配工程师 -->
                                <button th:if="${order.status == 'pending'}" 
                                        class="action-btn btn-assign" 
                                        onclick="showAssignModal(this)"
                                        th:data-order-id="${order.id}">
                                    <i class="bi bi-person-plus me-1"></i>分配工程师
                                </button>
                                
                                <!-- 待上门状态：可以开始维修 -->
                                <button th:if="${order.status == 'accepted'}" 
                                        class="action-btn btn-next" 
                                        onclick="updateStatus(this, 'processing')"
                                        th:data-order-id="${order.id}">
                                    <i class="bi bi-tools me-1"></i>开始维修
                                </button>
                                
                                <!-- 维修中状态：可以完成订单 -->
                                <button th:if="${order.status == 'processing'}" 
                                        class="action-btn btn-complete" 
                                        onclick="showCompleteModal(this)"
                                        th:data-order-id="${order.id}">
                                    <i class="bi bi-check-circle me-1"></i>完成订单
                                </button>
                                
                                <button class="action-btn btn-outline-secondary" 
                                        onclick="viewOrderDetail(this)"
                                        th:data-order-id="${order.id}">
                                    <i class="bi bi-eye me-1"></i>查看详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <nav th:if="${orderPage.pages > 1}" aria-label="订单分页">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" th:classappend="${orderPage.current == 1 ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/orders(page=${orderPage.current - 1}, status=${currentStatus})}">上一页</a>
                        </li>
                        
                        <li th:each="i : ${#numbers.sequence(1, orderPage.pages)}" 
                            class="page-item" 
                            th:classappend="${i == orderPage.current ? 'active' : ''}">
                            <a class="page-link" th:href="@{/admin/orders(page=${i}, status=${currentStatus})}" th:text="${i}">1</a>
                        </li>
                        
                        <li class="page-item" th:classappend="${orderPage.current == orderPage.pages ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/admin/orders(page=${orderPage.current + 1}, status=${currentStatus})}">下一页</a>
                        </li>
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 筛选订单
        function filterOrders(status) {
            const url = new URL(window.location);
            if (status) {
                url.searchParams.set('status', status);
            } else {
                url.searchParams.delete('status');
            }
            url.searchParams.set('page', '1');
            window.location.href = url.toString();
        }
        
        // 更新订单状态
        function updateStatus(button, newStatus) {
            const orderId = button.dataset.orderId;
            const statusText = {
                'accepted': '接单',
                'processing': '开始维修',
                'completed': '完成订单'
            };
            
            if (confirm(`确定要${statusText[newStatus]}吗？`)) {
                fetch(`/admin/api/orders/${orderId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `status=${newStatus}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert(data.message || '操作失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
            }
        }
        
        // 显示分配工程师模态框
        function showAssignModal(button) {
            const orderId = button.dataset.orderId;
            // 这里可以实现一个模态框来选择工程师
            // 暂时使用简单的prompt
            const engineerId = prompt('请输入工程师ID：');
            if (engineerId) {
                assignEngineer(orderId, engineerId);
            }
        }
        
        // 分配工程师
        function assignEngineer(orderId, engineerId) {
            fetch(`/admin/api/orders/${orderId}/assign`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `engineerId=${engineerId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || '分配失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }
        
        // 显示完成订单模态框
        function showCompleteModal(button) {
            const orderId = button.dataset.orderId;
            const remark = prompt('请输入完成备注（可选）：');
            updateStatusWithRemark(orderId, 'completed', remark);
        }
        
        // 带备注更新状态
        function updateStatusWithRemark(orderId, status, remark) {
            let body = `status=${status}`;
            if (remark) {
                body += `&remark=${encodeURIComponent(remark)}`;
            }
            
            fetch(`/admin/api/orders/${orderId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: body
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || '操作失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请稍后重试');
            });
        }
        
        // 查看订单详情
        function viewOrderDetail(button) {
            const orderId = button.dataset.orderId;
            // 这里可以实现订单详情页面或模态框
            alert('订单详情功能待实现，订单ID: ' + orderId);
        }
    </script>
</body>
</html>
